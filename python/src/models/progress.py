#!/usr/bin/env python3
"""
Progress tracking model for Product Hunt Python Scraper
"""

from dataclasses import dataclass
from typing import Optional
from datetime import datetime, date
import time


@dataclass
class FetchProgress:
    """Progress tracking for product fetching"""
    
    date: date
    last_page: int
    total_products: int
    completed: bool
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @classmethod
    def from_db_row(cls, row: dict) -> 'FetchProgress':
        """Create FetchProgress from database row"""
        return cls(
            date=row.get('date'),
            last_page=row.get('last_page', 0),
            total_products=row.get('total_products', 0),
            completed=row.get('completed', False),
            created_at=row.get('created_at'),
            updated_at=row.get('updated_at')
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary for database storage"""
        return {
            'date': self.date,
            'last_page': self.last_page,
            'total_products': self.total_products,
            'completed': self.completed,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    def __str__(self) -> str:
        """String representation"""
        status = "completed" if self.completed else f"page {self.last_page}"
        return f"FetchProgress({self.date}: {self.total_products} products, {status})"


@dataclass
class RateLimitInfo:
    """Rate limit information from API responses"""
    
    remaining: int
    limit: int
    reset_time: Optional[datetime] = None
    
    @classmethod
    def from_headers(cls, headers: dict) -> 'RateLimitInfo':
        """Create RateLimitInfo from HTTP headers"""
        # Product Hunt API rate limit headers (case-insensitive lookup)
        remaining = int(headers.get('x-rate-limit-remaining', headers.get('X-RateLimit-Remaining', 0)))
        limit = int(headers.get('x-rate-limit-limit', headers.get('X-RateLimit-Limit', 1000)))

        reset_time = None
        reset_header = headers.get('x-rate-limit-reset', headers.get('X-RateLimit-Reset'))
        if reset_header:
            try:
                # Product Hunt API returns reset time as seconds until reset (duration), not timestamp
                reset_in_seconds = int(reset_header)
                # Convert to absolute timestamp (current time + duration)
                reset_time = datetime.fromtimestamp(time.time() + reset_in_seconds)
            except:
                pass
        
        return cls(
            remaining=remaining,
            limit=limit,
            reset_time=reset_time
        )
    
    def is_exhausted(self, buffer: int = 100) -> bool:
        """Check if rate limit is exhausted (with buffer)"""
        return self.remaining <= buffer
    
    def get_usage_percentage(self) -> float:
        """Get rate limit usage as percentage"""
        if self.limit == 0:
            return 100.0
        return ((self.limit - self.remaining) / self.limit) * 100
    
    def __str__(self) -> str:
        """String representation"""
        return f"RateLimit({self.remaining}/{self.limit} remaining)"
