#!/usr/bin/env python3
"""
Product data model for Product Hunt Python Scraper
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from datetime import datetime, date


@dataclass
class Product:
    """Product data model matching Product Hunt GraphQL response (simplified to match PHP)"""

    # Core product information
    id: str
    name: str
    tagline: str
    description: str
    website: str
    url: str  # Product Hunt URL

    # Metrics
    votes_count: int
    comments_count: int
    reviews_rating: Optional[float]

    # Dates
    created_at: Optional[datetime]

    # Related data
    topics: List[Dict[str, Any]]
    
    @classmethod
    def from_graphql_response(cls, data: Dict[str, Any]) -> 'Product':
        """Create Product instance from GraphQL response data (simplified to match PHP)"""

        # Parse created_at date
        created_at = None
        if data.get('createdAt'):
            try:
                created_at = datetime.fromisoformat(data['createdAt'].replace('Z', '+00:00'))
            except:
                pass

        return cls(
            id=data.get('id', ''),
            name=data.get('name', ''),
            tagline=data.get('tagline', ''),
            description=data.get('description', ''),
            website=data.get('website', ''),
            url=data.get('url', ''),
            votes_count=data.get('votesCount', 0),
            comments_count=data.get('commentsCount', 0),
            reviews_rating=data.get('reviewsRating'),
            created_at=created_at,
            topics=data.get('topics', {}).get('edges', []) if data.get('topics') else []
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert Product to dictionary for database storage (simplified to match PHP)"""
        return {
            'id': self.id,
            'name': self.name,
            'tagline': self.tagline,
            'description': self.description,
            'website': self.website,
            'url': self.url,
            'votesCount': self.votes_count,
            'commentsCount': self.comments_count,
            'reviewsRating': self.reviews_rating,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'topics': self.topics
        }
    
    def get_launch_date(self) -> Optional[date]:
        """Get launch date as date object"""
        if self.featured_at:
            return self.featured_at.date()
        return None
    
    def get_topic_names(self) -> List[str]:
        """Get list of topic names"""
        return [topic.get('name', '') for topic in self.topics if topic.get('name')]
    
    def get_maker_names(self) -> List[str]:
        """Get list of maker names"""
        return [maker.get('name', '') for maker in self.makers if maker.get('name')]
    
    def get_hunter_name(self) -> str:
        """Get hunter name"""
        return self.hunter.get('name', '') if self.hunter else ''
    
    def __str__(self) -> str:
        """String representation of Product"""
        return f"Product(id={self.id}, name='{self.name}', votes={self.votes_count})"
    
    def __repr__(self) -> str:
        """Detailed string representation of Product"""
        return (f"Product(id={self.id}, name='{self.name}', tagline='{self.tagline}', "
                f"votes={self.votes_count}, comments={self.comments_count}, "
                f"featured_at={self.featured_at})")


@dataclass
class ProductPage:
    """Container for a page of products with pagination info"""
    
    products: List[Product]
    page_info: Dict[str, Any]
    total_count: int
    
    @classmethod
    def from_graphql_response(cls, data: Dict[str, Any]) -> 'ProductPage':
        """Create ProductPage from GraphQL response"""
        posts_data = data.get('posts', {})
        edges = posts_data.get('edges', [])
        
        # Convert edges to Product objects
        products = []
        for edge in edges:
            node = edge.get('node', {})
            if node:
                products.append(Product.from_graphql_response(node))
        
        return cls(
            products=products,
            page_info=posts_data.get('pageInfo', {}),
            total_count=posts_data.get('totalCount', 0)
        )
    
    def has_next_page(self) -> bool:
        """Check if there are more pages available"""
        return self.page_info.get('hasNextPage', False)
    
    def get_end_cursor(self) -> Optional[str]:
        """Get cursor for next page"""
        return self.page_info.get('endCursor')
    
    def __len__(self) -> int:
        """Number of products in this page"""
        return len(self.products)
    
    def __str__(self) -> str:
        """String representation of ProductPage"""
        return f"ProductPage({len(self.products)} products, has_next={self.has_next_page()})"
