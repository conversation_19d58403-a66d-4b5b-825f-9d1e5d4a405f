#!/usr/bin/env python3
"""
Playwright helper for URL resolution fallback with advanced stealth features

This module provides Playwright-based URL resolution as a fallback when
cloudscraper fails, with advanced anonymization and stealth capabilities.
"""

import asyncio
import logging
import json
import random
import time
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

try:
    from playwright.async_api import async_playwright, TimeoutError as PlaywrightTimeoutError
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    async_playwright = None
    PlaywrightTimeoutError = Exception


class PlaywrightHelper:
    """Playwright helper for URL resolution fallback with advanced stealth"""

    # Comprehensive user agent list for maximum anonymization
    USER_AGENTS = [
        # Desktop Chrome (Windows)
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36',

        # Desktop Chrome (macOS)
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',

        # Desktop Chrome (Linux)
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

        # Desktop Firefox (Windows)
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',

        # Desktop Firefox (macOS)
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/120.0',

        # Desktop Firefox (Linux)
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/120.0',

        # Desktop Safari (macOS)
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',

        # Desktop Edge (Windows)
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',

        # Desktop Edge (macOS)
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0',

        # Mobile Chrome (Android)
        'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 11; OnePlus 9) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 13; Samsung SM-S918B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',

        # Mobile Safari (iOS)
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1',

        # Mobile Firefox
        'Mozilla/5.0 (Mobile; rv:109.0) Gecko/109.0 Firefox/121.0',
        'Mozilla/5.0 (Android 13; Mobile; rv:109.0) Gecko/109.0 Firefox/121.0',
        'Mozilla/5.0 (Android 12; Mobile; rv:109.0) Gecko/109.0 Firefox/120.0',
    ]

    # Random viewport sizes for different device types
    VIEWPORTS = [
        # Desktop viewports
        {'width': 1920, 'height': 1080},
        {'width': 1366, 'height': 768},
        {'width': 1536, 'height': 864},
        {'width': 1440, 'height': 900},
        {'width': 1280, 'height': 720},
        {'width': 1600, 'height': 900},
        {'width': 2560, 'height': 1440},

        # Laptop viewports
        {'width': 1024, 'height': 768},
        {'width': 1280, 'height': 800},
        {'width': 1440, 'height': 900},

        # Tablet viewports
        {'width': 768, 'height': 1024},
        {'width': 1024, 'height': 768},
        {'width': 820, 'height': 1180},

        # Mobile viewports
        {'width': 375, 'height': 667},
        {'width': 414, 'height': 896},
        {'width': 390, 'height': 844},
        {'width': 360, 'height': 640},
    ]

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.available = PLAYWRIGHT_AVAILABLE

        if not self.available:
            self.logger.warning("Playwright not available - fallback disabled")
    
    def is_available(self) -> bool:
        """Check if Playwright is available"""
        return self.available
    
    def _get_random_stealth_config(self) -> Dict[str, Any]:
        """Generate random stealth configuration for maximum anonymization"""
        user_agent = random.choice(self.USER_AGENTS)
        viewport = random.choice(self.VIEWPORTS)

        # Determine if this should be a mobile session based on user agent
        is_mobile = 'Mobile' in user_agent or 'iPhone' in user_agent or 'Android' in user_agent

        # Random timezone from common ones
        timezones = [
            'America/New_York', 'America/Los_Angeles', 'America/Chicago', 'America/Denver',
            'Europe/London', 'Europe/Paris', 'Europe/Berlin', 'Europe/Rome',
            'Asia/Tokyo', 'Asia/Shanghai', 'Australia/Sydney', 'America/Toronto'
        ]

        # Random locale
        locales = ['en-US', 'en-GB', 'en-CA', 'en-AU', 'de-DE', 'fr-FR', 'es-ES', 'it-IT']

        return {
            'user_agent': user_agent,
            'viewport': viewport,
            'is_mobile': is_mobile,
            'timezone_id': random.choice(timezones),
            'locale': random.choice(locales),
            'color_scheme': random.choice(['light', 'dark']),
            'reduced_motion': random.choice(['reduce', 'no-preference']),
        }

    async def resolve_url(self, url: str, timeout: int = 30000) -> Dict[str, Any]:
        """
        Resolve URL using Playwright with advanced stealth features

        Args:
            url: URL to resolve
            timeout: Timeout in milliseconds

        Returns:
            Dict with keys: success, finalUrl, statusCode, status, error
        """
        if not self.available:
            return {
                'success': False,
                'error': 'Playwright not available'
            }

        # Generate random stealth configuration
        stealth_config = self._get_random_stealth_config()

        try:
            async with async_playwright() as p:
                # Launch browser with stealth options
                browser = await p.chromium.launch(
                    headless=True,
                    args=[
                        '--no-sandbox',
                        '--disable-blink-features=AutomationControlled',
                        '--disable-features=VizDisplayCompositor',
                        '--disable-extensions',
                        '--disable-plugins',
                        '--disable-images',  # Faster loading
                        '--disable-javascript',  # We don't need JS for URL resolution
                        '--disable-gpu',
                        '--no-first-run',
                        '--no-default-browser-check',
                        '--disable-dev-shm-usage',
                        '--disable-background-timer-throttling',
                        '--disable-backgrounding-occluded-windows',
                        '--disable-renderer-backgrounding',
                    ]
                )

                # Create context with stealth configuration
                context = await browser.new_context(
                    user_agent=stealth_config['user_agent'],
                    viewport=stealth_config['viewport'],
                    is_mobile=stealth_config['is_mobile'],
                    has_touch=stealth_config['is_mobile'],
                    timezone_id=stealth_config['timezone_id'],
                    locale=stealth_config['locale'],
                    color_scheme=stealth_config['color_scheme'],
                    reduced_motion=stealth_config['reduced_motion'],
                    ignore_https_errors=True,
                    java_script_enabled=False,  # Disable JS for faster loading

                    # Additional stealth headers
                    extra_http_headers={
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept-Encoding': 'gzip, deflate, br',
                        'DNT': '1',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                        'Sec-Fetch-Dest': 'document',
                        'Sec-Fetch-Mode': 'navigate',
                        'Sec-Fetch-Site': 'none',
                        'Sec-Fetch-User': '?1',
                        'Cache-Control': 'max-age=0',
                    }
                )

                # Clear any existing cookies/storage for maximum anonymity
                await context.clear_cookies()

                page = await context.new_page()

                # Add random delay before navigation (human-like behavior)
                await asyncio.sleep(random.uniform(1.0, 3.0))

                self.logger.debug(f"Using stealth config: {stealth_config['user_agent'][:50]}... | {stealth_config['viewport']} | {stealth_config['timezone_id']}")
                
                try:
                    # Navigate to URL with timeout and stealth behavior
                    response = await page.goto(
                        url,
                        wait_until='domcontentloaded',  # Faster than networkidle
                        timeout=timeout
                    )

                    # Add small random delay to mimic human behavior
                    await asyncio.sleep(random.uniform(0.5, 2.0))

                    # Get final URL after redirects
                    final_url = page.url
                    status_code = response.status if response else 0

                    # Determine status
                    status = 'up' if 200 <= status_code < 400 else 'down'

                    result = {
                        'success': True,
                        'finalUrl': final_url,
                        'statusCode': status_code,
                        'status': status
                    }

                    self.logger.debug(f"Playwright stealth resolved {url} -> {final_url} (HTTP {status_code})")
                    return result
                    
                except PlaywrightTimeoutError as e:
                    # Try to get URL from error or current page
                    try:
                        final_url = page.url
                        if final_url and final_url != url:
                            return {
                                'success': True,
                                'finalUrl': final_url,
                                'status': 'down',
                                'error': f'Timeout but got final URL: {str(e)}'
                            }
                    except:
                        pass
                    
                    return {
                        'success': False,
                        'error': f'Timeout: {str(e)}'
                    }
                    
                except Exception as e:
                    # Try to extract URL from error or get current page URL
                    try:
                        final_url = page.url
                        if final_url and final_url != url:
                            return {
                                'success': True,
                                'finalUrl': final_url,
                                'status': 'down',
                                'error': f'Error but got final URL: {str(e)}'
                            }
                    except:
                        pass
                    
                    # Try to extract URL from error message
                    error_url = self._extract_url_from_error(str(e))
                    if error_url:
                        return {
                            'success': True,
                            'finalUrl': error_url,
                            'status': 'down',
                            'error': f'Extracted from error: {str(e)}'
                        }
                    
                    return {
                        'success': False,
                        'error': str(e)
                    }
                    
                finally:
                    # Clean up browser and context
                    try:
                        await context.close()
                    except:
                        pass
                    try:
                        await browser.close()
                    except:
                        pass
                    
        except Exception as e:
            self.logger.error(f"Playwright setup failed: {e}")
            return {
                'success': False,
                'error': f'Playwright setup failed: {str(e)}'
            }
    
    def resolve_url_sync(self, url: str, timeout: int = 30000) -> Dict[str, Any]:
        """
        Synchronous wrapper for resolve_url with enhanced error handling

        Args:
            url: URL to resolve
            timeout: Timeout in milliseconds

        Returns:
            Dict with keys: success, finalUrl, statusCode, status, error
        """
        try:
            # Add small delay before starting for rate limiting
            time.sleep(random.uniform(0.5, 1.5))

            # Run the async function in a new event loop
            result = asyncio.run(self.resolve_url(url, timeout))

            # Add small delay after completion for rate limiting
            time.sleep(random.uniform(0.5, 1.0))

            return result
        except Exception as e:
            self.logger.error(f"Playwright sync wrapper failed: {e}")
            return {
                'success': False,
                'error': f'Sync wrapper failed: {str(e)}'
            }

    def clear_session_data(self):
        """
        Clear any cached session data for maximum anonymity
        This method is called between requests to ensure clean state
        """
        # Since we create fresh browser instances for each request,
        # this is mainly for future extensibility
        self.logger.debug("Session data cleared (fresh browser instances used)")

    def get_random_user_agent(self) -> str:
        """Get a random user agent from the comprehensive list"""
        return random.choice(self.USER_AGENTS)

    def get_random_viewport(self) -> Dict[str, int]:
        """Get a random viewport size"""
        return random.choice(self.VIEWPORTS)
    
    def _extract_url_from_error(self, error_message: str) -> Optional[str]:
        """
        Try to extract URL from Playwright error patterns
        
        Args:
            error_message: Error message from Playwright
            
        Returns:
            Extracted URL or None
        """
        import re
        
        # Common Playwright error patterns
        patterns = [
            r'page\.goto: [^"]*at (https?://[^\s]+)',
            r'navigating to "(https?://[^"]+)"',
            r'net::ERR_[A-Z_]+ at (https?://[^\s]+)',
            r'(https?://[^\s]+)',  # Generic URL pattern
        ]
        
        for pattern in patterns:
            match = re.search(pattern, error_message)
            if match:
                url = match.group(1)
                # Clean up any trailing characters
                url = url.rstrip('.,;')
                
                # Validate it's a reasonable URL
                try:
                    parsed = urlparse(url)
                    if parsed.scheme and parsed.netloc:
                        return url
                except:
                    continue
        
        return None


# Global Playwright helper instance
playwright_helper = PlaywrightHelper()
