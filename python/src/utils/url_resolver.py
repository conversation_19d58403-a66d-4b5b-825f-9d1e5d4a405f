#!/usr/bin/env python3
"""
URL resolution utilities using cloudscraper

This module provides URL resolution functionality equivalent to the PHP url_utils.php,
using cloudscraper for Cloudflare bypass and advanced web scraping capabilities.
"""

import re
import time
import random
import logging
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse
import cloudscraper

from .cloudscraper_helper import cloudscraper_helper
from .playwright_helper import playwright_helper


class URLUtils:
    """URL utilities for cleaning and following redirects"""
    
    # Realistic user agents to rotate through
    USER_AGENTS = [
        # Desktop Chrome
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        
        # Desktop Firefox
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
        'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0',
        
        # Desktop Safari
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Safari/605.1.15',
        
        # Mobile Chrome (Android)
        'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
        
        # Mobile Safari (iOS)
        'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
        'Mozilla/5.0 (iPad; CPU OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    ]
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def clean_url(cls, url: str) -> str:
        """Keep URL parameters but ensure URL is properly formatted"""
        if not url:
            return ''
        
        # Just trim whitespace and return the URL as-is (keeping parameters)
        url = url.strip()
        
        # Ensure URL has a scheme
        if not re.match(r'^https?://', url):
            url = 'https://' + url
        
        return url
    
    @classmethod
    def is_likely_redirect(cls, url: str) -> bool:
        """Check if URL is likely a redirect URL (contains common redirect patterns)"""
        redirect_patterns = [
            'producthunt.com/r/',
            'bit.ly/',
            'tinyurl.com/',
            'short.link/',
            'go.',  # go.example.com
            'link.',
            'redirect',
            'r.',  # r.example.com
            'l.',  # l.example.com
        ]
        
        for pattern in redirect_patterns:
            if pattern in url:
                return True
        
        return False
    
    @classmethod
    def remove_url_parameters(cls, url: str) -> str:
        """Remove ProductHunt reference parameters while keeping other URL parameters"""
        if not url:
            return ''
        
        # Parse URL
        parsed = urlparse(url)
        if not parsed.query:
            return url
        
        # Parse query parameters
        params = parse_qs(parsed.query, keep_blank_values=True)
        
        # Remove ProductHunt reference parameters
        params.pop('ref', None)  # Remove ref parameter entirely
        
        # Rebuild query string
        if params:
            # Flatten the parameter values (parse_qs returns lists)
            flat_params = {}
            for key, values in params.items():
                if values:
                    flat_params[key] = values[0]  # Take first value
            
            new_query = urlencode(flat_params)
        else:
            new_query = ''
        
        # Rebuild URL
        new_parsed = parsed._replace(query=new_query)
        return urlunparse(new_parsed)
    
    @classmethod
    def get_random_user_agent(cls) -> str:
        """Get a random user agent from the pool"""
        return random.choice(cls.USER_AGENTS)
    
    def get_final_url_with_status(self, url: str, max_redirects: int = 10, timeout: int = 120) -> Dict[str, Any]:
        """
        Follow redirects and get the final URL with status information using cloudscraper
        
        Args:
            url: URL to resolve
            max_redirects: Maximum number of redirects to follow
            timeout: Request timeout in seconds
            
        Returns:
            Dict with keys: url, status, http_code
        """
        if not url:
            return {'url': '', 'status': 'down', 'http_code': 0}
        
        # Safety precaution: Random delay for Product Hunt domains
        if 'producthunt.com' in url:
            base_delay = random.uniform(7.5, 10.5)  # 7.5 to 10.5 seconds
            jitter = random.uniform(-1.5, 1.5)  # Add/subtract up to 1.5 seconds
            total_delay = max(6.0, base_delay + jitter)  # Ensure minimum 6 seconds
            print(f"⏳ Safety delay: {total_delay:.2f}s for Product Hunt domain...")
            time.sleep(total_delay)
        
        # Clean the URL first
        url = self.clean_url(url)
        
        try:
            # Use cloudscraper for the request
            scraper = cloudscraper_helper.get_scraper()
            
            # Set a random user agent
            scraper.headers.update({
                'User-Agent': self.get_random_user_agent()
            })
            
            # Make HEAD request first (faster)
            try:
                response = scraper.head(
                    url,
                    timeout=timeout,
                    allow_redirects=True
                )
                
                final_url = response.url
                http_code = response.status_code
                
            except Exception as head_error:
                # If HEAD fails, try GET request
                self.logger.debug(f"HEAD request failed, trying GET: {head_error}")
                
                response = scraper.get(
                    url,
                    timeout=timeout,
                    allow_redirects=True
                )
                
                final_url = response.url
                http_code = response.status_code
                print("🔄 HEAD request failed, GET request succeeded")
            
            # Determine status based on HTTP code
            status = 'up' if 200 <= http_code < 400 else 'down'
            
            # Log the result
            if status == 'up':
                if final_url != url:
                    print("🔄 Followed redirect(s) to final URL")
                print(f"✅ Final URL found with HTTP {http_code}")
            else:
                if final_url != url:
                    print("🔄 Followed redirect(s) to final URL")
                print(f"❌ Error: HTTP {http_code} for final URL, but URL resolved")
            
            return {
                'url': final_url,
                'status': status,
                'http_code': http_code
            }
            
        except Exception as e:
            self.logger.error(f"Cloudscraper request failed: {e}")
            print(f"❌ Error: Could not reach {url} - {e}")
            
            # Return empty URL for complete failures
            return {
                'url': '',
                'status': 'down',
                'http_code': 0
            }


# Global URL utils instance
url_utils = URLUtils()
