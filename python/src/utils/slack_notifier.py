#!/usr/bin/env python3
"""
Slack notification helper for Python scripts

This module provides Slack notifications compatible with the PHP SlackNotifier class.
Uses Slack Incoming Webhooks for reliable message delivery.
"""

import os
import json
import requests
import logging
import socket
from datetime import datetime
from typing import Dict, Any, Optional


class SlackNotifier:
    """Slack notification helper class"""
    
    def __init__(self):
        self.webhook_url = os.getenv('SLACK_WEBHOOK_URL', '')
        self.enabled = os.getenv('SLACK_NOTIFICATIONS_ENABLED', 'false').lower() in ('true', '1', 'yes', 'on')
        self.logger = logging.getLogger(__name__)
    
    def notify(self, message: str, level: str = 'info', context: Optional[Dict[str, Any]] = None) -> bool:
        """
        Send a notification to Slack
        
        Args:
            message: The message to send
            level: The severity level (info, warning, error, critical)
            context: Additional context data
            
        Returns:
            bool: Success status
        """
        if not self.enabled:
            return True  # Silently succeed if disabled
        
        if not self.webhook_url:
            self.logger.error("Slack webhook URL not configured")
            return False
        
        payload = self._build_payload(message, level, context or {})
        return self._send_to_slack(payload)
    
    def critical(self, message: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Send critical error notification"""
        return self.notify(message, 'critical', context)
    
    def error(self, message: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Send error notification"""
        return self.notify(message, 'error', context)
    
    def warning(self, message: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Send warning notification"""
        return self.notify(message, 'warning', context)
    
    def info(self, message: str, context: Optional[Dict[str, Any]] = None) -> bool:
        """Send info notification"""
        return self.notify(message, 'info', context)
    
    def _build_payload(self, message: str, level: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Build Slack payload"""
        emoji = self._get_level_emoji(level)
        color = self._get_level_color(level)
        
        hostname = socket.gethostname() or 'unknown'
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S %Z')
        
        payload = {
            'username': 'Product Hunt Monitor',
            'icon_emoji': ':robot_face:',
            'attachments': [
                {
                    'color': color,
                    'title': f'{emoji} {level.title()} Alert',
                    'text': message,
                    'fields': [
                        {
                            'title': 'Server',
                            'value': hostname,
                            'short': True
                        },
                        {
                            'title': 'Time',
                            'value': timestamp,
                            'short': True
                        }
                    ],
                    'footer': 'Product Hunt System',
                    'ts': int(datetime.now().timestamp())
                }
            ]
        }
        
        # Add context fields if provided
        if context:
            for key, value in context.items():
                field_title = key.replace('_', ' ').title()
                field_value = json.dumps(value) if isinstance(value, (dict, list)) else str(value)
                payload['attachments'][0]['fields'].append({
                    'title': field_title,
                    'value': field_value,
                    'short': len(field_value) < 50
                })
        
        return payload
    
    def _get_level_emoji(self, level: str) -> str:
        """Get emoji for level"""
        emojis = {
            'info': ':information_source:',
            'warning': ':warning:',
            'error': ':x:',
            'critical': ':rotating_light:'
        }
        return emojis.get(level, ':grey_question:')
    
    def _get_level_color(self, level: str) -> str:
        """Get color for level"""
        colors = {
            'info': '#36a64f',      # Green
            'warning': '#ff9500',   # Orange
            'error': '#ff0000',     # Red
            'critical': '#8B0000'   # Dark Red
        }
        return colors.get(level, '#cccccc')
    
    def _send_to_slack(self, payload: Dict[str, Any]) -> bool:
        """Send payload to Slack using requests"""
        try:
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={
                    'Content-Type': 'application/json',
                    'User-Agent': 'ProductHunt-Monitor/1.0'
                },
                timeout=10
            )
            
            if response.status_code != 200:
                self.logger.error(f"Slack notification failed - HTTP {response.status_code}: {response.text}")
                return False
            
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Slack notification failed - Request error: {e}")
            return False
    
    def test(self) -> bool:
        """Test the Slack connection"""
        return self.info("🧪 Slack notification test - system is working correctly!")
