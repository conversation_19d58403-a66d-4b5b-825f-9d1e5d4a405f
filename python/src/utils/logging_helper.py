#!/usr/bin/env python3
"""
Logging helper for Product Hunt Python Scraper

This module provides logging configuration that matches the PHP version's output format
for seamless integration with existing monitoring and log parsing.
"""

import logging
import sys
import os
from datetime import datetime
from typing import Optional

from ..config.settings import settings


def setup_logging(
    name: Optional[str] = None,
    log_file: Optional[str] = None,
    console_output: bool = True
) -> logging.Logger:
    """
    Set up logging configuration
    
    Args:
        name: Logger name (defaults to __name__)
        log_file: Optional log file path
        console_output: Whether to output to console
    
    Returns:
        Configured logger instance
    """
    if name is None:
        name = __name__
    
    logger = logging.getLogger(name)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Set log level
    log_config = settings.get_logging_config()
    level = getattr(logging, log_config['level'].upper(), logging.INFO)
    logger.setLevel(level)
    
    # Create formatter that matches PHP output style
    formatter = PHPStyleFormatter()
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if log_file:
        # Ensure log directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


class PHPStyleFormatter(logging.Formatter):
    """Custom formatter that matches PHP script output style"""
    
    def __init__(self):
        super().__init__()
        self.emoji_map = {
            'DEBUG': '🔍',
            'INFO': 'ℹ️',
            'WARNING': '⚠️',
            'ERROR': '❌',
            'CRITICAL': '🚨'
        }
    
    def format(self, record):
        # Get the raw message
        raw_message = record.getMessage()

        # Check if message already starts with an emoji
        # Common emojis used in the codebase (expanded list)
        emoji_prefixes = ['🚀', '✅', '🔍', '📄', '📡', '🚦', '🛡️', '💾', '⚠️', '❌', '🚨', '🔄',
                         '📊', '💤', '🧪', '📅', '🕐', '⏰', '⏱️', '✨', '🎉', '🔧']
        message_starts_with_emoji = any(raw_message.startswith(emoji) for emoji in emoji_prefixes)

        if message_starts_with_emoji:
            # Message already has emoji, don't add prefix
            message = raw_message
        else:
            # Get emoji for log level and add prefix
            emoji = self.emoji_map.get(record.levelname, 'ℹ️')
            message = f"{emoji} {raw_message}"

        return message
    
    def formatException(self, ei):
        """Format exception information"""
        result = super().formatException(ei)
        return f"❌ Exception: {result}"


class ProductHuntLogger:
    """Logger wrapper with Product Hunt specific methods"""
    
    def __init__(self, name: str, log_file: Optional[str] = None):
        self.logger = setup_logging(name, log_file)
    
    def info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def error(self, message: str):
        """Log error message"""
        self.logger.error(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
    
    def rate_limit_info(self, remaining: int, limit: int):
        """Log rate limit information (matches PHP format)"""
        # Rate limit info is now handled in _update_rate_limit_info() with full details
        pass

    def fetch_progress(self, page: int, products: int):
        """Log fetch progress (matches PHP format)"""
        print(f"📄 Page {page} completed, {products} products fetched so far")
    
    def product_saved(self, name: str, action: str = "inserted"):
        """Log product save action (matches PHP format)"""
        if action == "inserted":
            # Print directly to avoid double emoji from formatter
            print(f"✅ Inserted product: {name}")
        else:
            print(f"🔄 Updated product: {name}")
    
    def cloudflare_bypass(self, url: str):
        """Log Cloudflare bypass attempt"""
        print(f"🛡️ Bypassing Cloudflare protection for: {url}")

    def api_request(self, endpoint: str):
        """Log API request"""
        print(f"📡 Making API request to: {endpoint}")

    def database_operation(self, operation: str, table: str):
        """Log database operation"""
        print(f"💾 Database {operation} on table: {table}")


def get_logger(name: str, log_file: Optional[str] = None) -> ProductHuntLogger:
    """Get a Product Hunt logger instance"""
    return ProductHuntLogger(name, log_file)
