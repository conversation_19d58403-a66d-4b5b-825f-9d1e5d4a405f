#!/usr/bin/env python3
"""
Cloudscraper helper for bypassing Cloudflare protection

This module provides a configured cloudscraper instance with advanced stealth mode
for bypassing Cloudflare protection on Product Hunt and other websites.
"""

import cloudscraper
import logging
import time
import random
from typing import Dict, Any, Optional

from src.config.settings import settings


class CloudscraperHelper:
    """Helper class for managing cloudscraper instances"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = settings.get_cloudscraper_config()
        self._scraper = None
    
    def get_scraper(self) -> cloudscraper.CloudScraper:
        """Get configured cloudscraper instance"""
        if self._scraper is None:
            self._scraper = self._create_scraper()
        return self._scraper
    
    def _create_scraper(self) -> cloudscraper.CloudScraper:
        """Create new cloudscraper instance with compatible configuration"""
        try:
            # Try basic cloudscraper configuration first
            scraper = cloudscraper.create_scraper(
                # Browser configuration
                browser={
                    'browser': 'firefox',
                    'platform': 'windows',
                    'mobile': False
                },
                # Challenge handling delay
                delay=5
            )

            self.logger.info("✅ Created cloudscraper with Firefox configuration")
            self.logger.info(f"   User-Agent: {scraper.headers.get('User-Agent', 'Unknown')}")
            return scraper

        except Exception as e:
            self.logger.error(f"Failed to create cloudscraper with Firefox config: {e}")
            # Fallback to most basic configuration
            try:
                self.logger.warning("Trying most basic cloudscraper configuration...")
                scraper = cloudscraper.create_scraper()
                self.logger.info("✅ Created cloudscraper with default configuration")
                return scraper
            except Exception as fallback_error:
                self.logger.error(f"Basic fallback also failed: {fallback_error}")
                raise
    
    def make_request(self, method: str, url: str, **kwargs) -> cloudscraper.requests.Response:
        """Make HTTP request with cloudscraper"""
        scraper = self.get_scraper()
        
        # Add human-like delay before request
        self._add_human_delay()
        
        try:
            self.logger.debug(f"Making {method.upper()} request to: {url}")
            response = scraper.request(method, url, **kwargs)
            
            self.logger.debug(f"Response status: {response.status_code}")
            return response
            
        except Exception as e:
            self.logger.error(f"Request failed: {e}")
            raise
    
    def get(self, url: str, **kwargs) -> cloudscraper.requests.Response:
        """Make GET request"""
        return self.make_request('GET', url, **kwargs)
    
    def post(self, url: str, **kwargs) -> cloudscraper.requests.Response:
        """Make POST request"""
        return self.make_request('POST', url, **kwargs)
    
    def _add_human_delay(self):
        """Add human-like delay between requests"""
        min_delay = self.config['min_delay']
        max_delay = self.config['max_delay']
        delay = random.uniform(min_delay, max_delay)
        
        self.logger.debug(f"Adding human delay: {delay:.2f} seconds")
        time.sleep(delay)
    
    def get_session_info(self) -> Dict[str, Any]:
        """Get information about the current scraper session"""
        scraper = self.get_scraper()
        return {
            'user_agent': scraper.headers.get('User-Agent', 'Unknown'),
            'cookies': len(scraper.cookies),
            'headers': dict(scraper.headers)
        }
    
    def reset_session(self):
        """Reset the scraper session (create new instance)"""
        self.logger.info("Resetting cloudscraper session")
        self._scraper = None


# Global cloudscraper helper instance
cloudscraper_helper = CloudscraperHelper()
