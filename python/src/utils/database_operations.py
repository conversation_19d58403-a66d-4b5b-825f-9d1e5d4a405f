#!/usr/bin/env python3
"""
Database operations for Product Hunt Python Scraper

This module provides database operations compatible with the existing PHP schema.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date
import json

from ..config.database import db_config


class DatabaseOperations:
    """Database operations for Product Hunt data"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def save_product(self, product_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Save or update a product in the database
        
        Args:
            product_data: Product data dictionary
            
        Returns:
            Tuple of (success: bool, action: str) where action is 'inserted' or 'updated'
        """
        try:
            with db_config.get_transaction() as cursor:
                # Check if product already exists (using Product Hunt ID as primary key)
                cursor.execute(
                    "SELECT COUNT(*) as count FROM products WHERE id = %s",
                    (product_data['id'],)
                )
                existing = cursor.fetchone()

                if existing and existing['count'] > 0:
                    # Update existing product
                    success = self._update_product(cursor, product_data)
                    return success, 'updated'
                else:
                    # Insert new product
                    success = self._insert_product(cursor, product_data)
                    return success, 'inserted'
                    
        except Exception as e:
            self.logger.error(f"Failed to save product {product_data.get('name', 'Unknown')}: {e}")
            return False, 'error'
    
    def _insert_product(self, cursor, product_data: Dict[str, Any]) -> bool:
        """Insert new product into database (matching PHP schema)"""
        try:
            # Prepare product data for insertion
            insert_data = self._prepare_product_data(product_data)

            # Insert product using PHP-compatible schema
            insert_query = """
                INSERT INTO products (
                    id, name, tagline, description, votes_count, comments_count, review_rating,
                    product_url, date_created, url
                ) VALUES (
                    %(id)s, %(name)s, %(tagline)s, %(description)s, %(votes_count)s,
                    %(comments_count)s, %(review_rating)s, %(product_url)s, %(date_created)s, %(url)s
                )
            """

            cursor.execute(insert_query, insert_data)
            product_hunt_id = product_data['id']

            # Insert topics/categories using Product Hunt ID
            self._insert_product_categories(cursor, product_hunt_id, product_data.get('topics', []))

            self.logger.debug(f"Inserted product: {product_data.get('name')} (ID: {product_hunt_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to insert product: {e}")
            return False
    
    def _update_product(self, cursor, product_data: Dict[str, Any]) -> bool:
        """Update existing product in database (matching PHP schema)"""
        try:
            # Prepare product data for update
            update_data = self._prepare_product_data(product_data)

            # Update product using PHP-compatible schema
            update_query = """
                UPDATE products SET
                    name = %(name)s, tagline = %(tagline)s, description = %(description)s,
                    votes_count = %(votes_count)s, comments_count = %(comments_count)s,
                    review_rating = %(review_rating)s, product_url = %(product_url)s,
                    date_created = %(date_created)s, url = %(url)s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """

            cursor.execute(update_query, update_data)
            product_hunt_id = product_data['id']

            # Update topics/categories using Product Hunt ID
            self._update_product_categories(cursor, product_hunt_id, product_data.get('topics', []))

            self.logger.debug(f"Updated product: {product_data.get('name')} (ID: {product_hunt_id})")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update product: {e}")
            return False
    
    def _prepare_product_data(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare product data for database insertion/update (matching PHP schema)"""

        # Parse launch date from createdAt (like PHP script)
        date_created = None
        if 'createdAt' in product_data and product_data['createdAt']:
            try:
                created_dt = datetime.fromisoformat(product_data['createdAt'].replace('Z', '+00:00'))
                date_created = created_dt.strftime('%Y-%m-%d')
            except:
                pass

        # Clean URLs like PHP script does
        website_url = self._clean_website_url(product_data.get('website', ''))
        product_url = self._clean_website_url(product_data.get('url', ''))

        return {
            'id': product_data.get('id'),  # Product Hunt ID as primary key
            'name': product_data.get('name', ''),
            'tagline': product_data.get('tagline', ''),
            'description': product_data.get('description', ''),
            'votes_count': product_data.get('votesCount', 0),
            'comments_count': product_data.get('commentsCount', 0),
            'review_rating': product_data.get('reviewsRating'),  # Can be NULL
            'product_url': product_url,
            'date_created': date_created,
            'url': website_url
        }

    def _clean_website_url(self, url: str) -> str:
        """Clean website URL by removing parameters (matching PHP logic)"""
        if not url:
            return ''

        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)

            # Rebuild URL without query parameters and fragments
            clean_url = ''
            if parsed.scheme:
                clean_url += parsed.scheme + '://'
            if parsed.username:
                clean_url += parsed.username
                if parsed.password:
                    clean_url += ':' + parsed.password
                clean_url += '@'
            if parsed.hostname:
                clean_url += parsed.hostname
            if parsed.port:
                clean_url += ':' + str(parsed.port)
            if parsed.path:
                clean_url += parsed.path

            return clean_url
        except:
            return url  # Return original if parsing fails
    
    def _get_screenshot_url(self, product_data: Dict[str, Any]) -> str:
        """Extract screenshot URL from product data"""
        try:
            media = product_data.get('media', [])
            for item in media:
                if item.get('type') == 'image':
                    return item.get('url', '')
            return ''
        except:
            return ''
    
    def _get_gallery_images(self, product_data: Dict[str, Any]) -> List[str]:
        """Extract gallery images from product data"""
        try:
            images = []
            media = product_data.get('media', [])
            for item in media:
                if item.get('type') == 'image' and item.get('url'):
                    images.append(item['url'])
            return images
        except:
            return []
    
    def _get_makers(self, product_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Extract makers from product data"""
        try:
            makers = []
            for maker in product_data.get('makers', []):
                makers.append({
                    'id': maker.get('id', ''),
                    'name': maker.get('name', ''),
                    'username': maker.get('username', '')
                })
            return makers
        except:
            return []
    
    def _get_hunter(self, product_data: Dict[str, Any]) -> Dict[str, str]:
        """Extract hunter from product data"""
        try:
            hunter = product_data.get('hunter', {})
            if hunter:
                return {
                    'id': hunter.get('id', ''),
                    'name': hunter.get('name', ''),
                    'username': hunter.get('username', '')
                }
            return {}
        except:
            return {}
    
    def _get_product_links(self, product_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """Extract product links from product data"""
        try:
            links = []
            for link in product_data.get('productLinks', []):
                links.append({
                    'type': link.get('type', ''),
                    'url': link.get('url', '')
                })
            return links
        except:
            return []
    
    def _insert_product_categories(self, cursor, product_id: str, topics: List[Dict[str, Any]]):
        """Insert product categories (matching PHP schema)"""
        try:
            # Clear existing categories for this product
            cursor.execute("DELETE FROM product_categories WHERE product_id = %s", (product_id,))

            # Process topics like PHP script does - topics is list of edges
            for topic_edge in topics:
                # Extract topic name from the edge structure: {'node': {'name': 'Topic Name'}}
                topic_node = topic_edge.get('node', {})
                topic_name = topic_node.get('name', '').strip()

                if not topic_name:
                    continue

                # Get or create category (using CategoryManager logic)
                category_id = self._get_or_create_category(cursor, topic_name)

                if category_id:
                    # Link product to category
                    cursor.execute(
                        "INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (%s, %s)",
                        (product_id, category_id)
                    )
        except Exception as e:
            self.logger.error(f"Failed to insert product categories: {e}")

    def _update_product_categories(self, cursor, product_id: str, topics: List[Dict[str, Any]]):
        """Update product categories (same as insert)"""
        self._insert_product_categories(cursor, product_id, topics)

    def _get_or_create_category(self, cursor, category_name: str) -> Optional[int]:
        """Get or create category (matching PHP CategoryManager logic)"""
        try:
            # Create slug like PHP CategoryManager does
            slug = self._create_slug(category_name)

            # Check if category exists by slug (like PHP does)
            cursor.execute("SELECT id FROM categories WHERE slug = %s", (slug,))
            result = cursor.fetchone()

            if result:
                return result['id']

            # Create new category with both name and slug (like PHP)
            cursor.execute(
                "INSERT IGNORE INTO categories (name, slug) VALUES (%s, %s)",
                (category_name, slug)
            )
            return cursor.lastrowid

        except Exception as e:
            self.logger.error(f"Failed to get/create category '{category_name}': {e}")
            return None

    def _create_slug(self, text: str) -> str:
        """Create URL-friendly slug from text (matching PHP CategoryManager logic)"""
        import re

        # Convert to lowercase
        text = text.lower()

        # Remove non-alphanumeric characters except spaces and hyphens
        text = re.sub(r'[^a-z0-9\s-]', '', text)

        # Replace multiple spaces/hyphens with single hyphen
        text = re.sub(r'[\s-]+', '-', text)

        # Remove leading/trailing hyphens
        return text.strip('-')
    
    def get_fetch_progress(self, date_str: str) -> Dict[str, Any]:
        """Get fetch progress for a specific date (matching PHP schema)"""
        try:
            with db_config.get_cursor() as cursor:
                cursor.execute(
                    """
                    SELECT * FROM fetch_progress
                    WHERE target_date = %s
                    AND status IN ('in_progress', 'rate_limited')
                    ORDER BY updated_at DESC
                    LIMIT 1
                    """,
                    (date_str,)
                )
                result = cursor.fetchone()
                if result:
                    return {
                        'last_page': result.get('page_count', 0),
                        'total_products': result.get('products_fetched', 0),
                        'completed': result.get('status') == 'completed',
                        'pagination_cursor': result.get('pagination_cursor')
                    }
                return {}
        except Exception as e:
            self.logger.error(f"Failed to get fetch progress: {e}")
            return {}

    def update_fetch_progress(self, date_str: str, page: int, total_products: int, completed: bool = False, cursor: str = None, status: str = None):
        """Update fetch progress for a specific date (matching PHP schema)"""
        try:
            with db_config.get_transaction() as db_cursor:
                if status is None:
                    status = 'completed' if completed else 'in_progress'

                # Check if progress exists
                db_cursor.execute(
                    "SELECT id FROM fetch_progress WHERE target_date = %s",
                    (date_str,)
                )
                exists = db_cursor.fetchone()

                if exists:
                    # Update existing progress
                    db_cursor.execute(
                        """
                        UPDATE fetch_progress
                        SET pagination_cursor = %s, page_count = %s, products_fetched = %s,
                            status = %s, updated_at = NOW()
                        WHERE target_date = %s
                        """,
                        (cursor, page, total_products, status, date_str)
                    )
                else:
                    # Create new progress record
                    db_cursor.execute(
                        """
                        INSERT INTO fetch_progress (target_date, pagination_cursor, page_count, products_fetched, status)
                        VALUES (%s, %s, %s, %s, %s)
                        """,
                        (date_str, cursor, page, total_products, status)
                    )
        except Exception as e:
            self.logger.error(f"Failed to update fetch progress: {e}")


# Global database operations instance
db_ops = DatabaseOperations()
