#!/usr/bin/env python3
"""
Product Hunt GraphQL API fetcher with Cloudflare bypass

This module handles fetching products from Product Hunt GraphQL API using
the working Firefox + js2py cloudscraper configuration.
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, date, timedelta
import logging

try:
    from ..utils.cloudscraper_helper import cloudscraper_helper
    from ..utils.database_operations import db_ops
    from ..utils.logging_helper import get_logger
    from ..config.settings import settings
    from ..models.product import Product, ProductPage
    from ..models.progress import RateLimitInfo, FetchProgress
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from utils.cloudscraper_helper import cloudscraper_helper
    from utils.database_operations import db_ops
    from utils.logging_helper import get_logger
    from config.settings import settings
    from models.product import Product, ProductPage
    from models.progress import RateLimitInfo, FetchProgress


class ProductHuntAPI:
    """Product Hunt GraphQL API client with Cloudflare bypass"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.api_url = 'https://api.producthunt.com/v2/api/graphql'
        self.api_key = settings.get_api_key()
        self.batch_size = settings.get('DEFAULT_BATCH_SIZE', 20)
        self.rate_limit_buffer = settings.get('RATE_LIMIT_BUFFER', 10)  # Conservative like PHP (10 credits)
        self.current_rate_limit = None

        if not self.api_key:
            raise ValueError("Product Hunt API key not configured")
    
    def fetch_products_for_date(self, target_date: date, max_pages: int = 0) -> Tuple[int, bool]:
        """
        Fetch all products for a specific date

        Args:
            target_date: Date to fetch products for
            max_pages: Maximum pages to fetch (0 = unlimited)

        Returns:
            Tuple of (total_products_fetched, completed)
        """
        # Log date filtering like PHP version
        print(f"🔍 Filtering for date {target_date} (Pacific Time)")
        date_str = target_date.strftime('%Y-%m-%d')
        print(f"🚀 Starting product fetch for date: {date_str}")

        # Check existing progress
        progress = db_ops.get_fetch_progress(date_str)
        start_page = progress.get('last_page', 0) + 1 if progress else 1
        total_fetched = progress.get('total_products', 0) if progress else 0
        
        if progress and progress.get('completed'):
            print(f"✅ Date {date_str} already completed with {total_fetched} products")
            return total_fetched, True

        print(f"📄 Starting from page {start_page}, {total_fetched} products already fetched")
        
        page = start_page
        cursor = None
        completed = False
        
        try:
            while True:
                # Check rate limit before making request
                if not self._check_rate_limit():
                    self.logger.warning("⚠️  Rate limit exhausted, stopping fetch")
                    break

                try:
                    # Fetch page
                    print(f"📄 Fetching page {page} for {date_str}...")
                    product_page = self._fetch_posts_page(target_date, cursor, self.batch_size)

                    if not product_page or len(product_page) == 0:
                        print("📄 No more products found")
                        completed = True
                        break

                except Exception as e:
                    # Handle rate limit errors like PHP version
                    if 'Rate limit reached' in str(e):
                        self.logger.warning(f"⚠️  {e}")
                        self.logger.info(f"📊 Successfully fetched {total_fetched} products before hitting rate limit.")

                        # Save progress with rate_limited status so we can resume
                        db_ops.update_fetch_progress(date_str, page, total_fetched, False, cursor, 'rate_limited')
                        self.logger.info(f"💾 Progress saved - will resume from page {page + 1} after rate limit reset")
                        break
                    else:
                        # Re-raise other exceptions
                        raise e
                
                # Save products
                saved_count = 0
                for product in product_page.products:
                    success, action = db_ops.save_product(product.to_dict())
                    if success:
                        saved_count += 1
                        if action == 'inserted':
                            self.logger.product_saved(product.name, 'inserted')
                        else:
                            self.logger.product_saved(product.name, 'updated')
                
                total_fetched += saved_count
                self.logger.fetch_progress(page, total_fetched)
                
                # Update progress
                db_ops.update_fetch_progress(date_str, page, total_fetched, False, cursor)
                
                # Check if we should continue
                if not product_page.has_next_page():
                    print("📄 Reached last page")
                    completed = True
                    break

                if max_pages > 0 and page >= max_pages:
                    print(f"📄 Reached maximum pages limit: {max_pages}")
                    break
                
                # Prepare for next page
                cursor = product_page.get_end_cursor()
                page += 1
                
                # Add delay between requests
                time.sleep(2)
        
        except KeyboardInterrupt:
            self.logger.warning("⚠️  Fetch interrupted by user")
        except Exception as e:
            self.logger.error(f"❌ Fetch failed: {e}")
            raise
        
        # Mark as completed if we finished
        if completed:
            db_ops.update_fetch_progress(date_str, page, total_fetched, True, cursor)
            print(f"✅ Completed fetch for {date_str}: {total_fetched} products")

        # Output final count for continuous_fetch.php parsing
        print(f"✅ Fetched {total_fetched} products across {page} pages")

        return total_fetched, completed
    
    def _fetch_posts_page(self, target_date: date, cursor: Optional[str] = None, 
                         first: int = 20) -> Optional[ProductPage]:
        """Fetch a single page of products"""
        try:
            # Build GraphQL query
            query = self._build_posts_query(target_date, cursor, first)
            
            # Make API request with cloudscraper
            response = self._make_graphql_request(query)
            
            if response and 'data' in response:
                return ProductPage.from_graphql_response(response['data'])
            else:
                self.logger.error("Invalid API response format")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to fetch posts page: {e}")
            return None
    
    def _build_posts_query(self, target_date: date, cursor: Optional[str] = None,
                          first: int = 20) -> Dict[str, Any]:
        """Build GraphQL query for fetching posts (exactly matching PHP script)"""
        from datetime import datetime

        # Use proper America/Los_Angeles timezone (handles DST automatically like PHP)
        try:
            from zoneinfo import ZoneInfo
            la_tz = ZoneInfo('America/Los_Angeles')
        except ImportError:
            # Fallback to pytz if zoneinfo not available
            import pytz
            la_tz = pytz.timezone('America/Los_Angeles')

        # Convert to Pacific Time properly (like PHP script)
        start_time = datetime.combine(target_date, datetime.min.time()).replace(tzinfo=la_tz)
        end_time = datetime.combine(target_date, datetime.max.time()).replace(tzinfo=la_tz)

        posted_after = start_time.isoformat()
        posted_before = end_time.isoformat()

        # Build GraphQL query with variables (exactly like PHP version)
        query = '''
            query fetchPosts($first: Int, $after: String, $postedAfter: DateTime, $postedBefore: DateTime) {
                posts(first: $first, after: $after, postedAfter: $postedAfter, postedBefore: $postedBefore) {
                    edges {
                        node {
                            id
                            name
                            tagline
                            description
                            votesCount
                            commentsCount
                            website
                            url
                            createdAt
                            reviewsRating
                            topics {
                                edges {
                                    node {
                                        name
                                    }
                                }
                            }
                        }
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                }
            }
        '''

        # Build variables (exactly like PHP version)
        variables = {
            'first': first,
            'postedAfter': posted_after,
            'postedBefore': posted_before
        }

        if cursor:
            variables['after'] = cursor

        return {
            'query': query,
            'variables': variables
        }
    
    def _make_graphql_request(self, query: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make GraphQL request using cloudscraper"""
        headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}',
            'Host': 'api.producthunt.com',
            'Origin': 'https://www.producthunt.com',
            'Referer': 'https://www.producthunt.com/',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }
        
        try:
            self.logger.api_request(self.api_url)
            
            response = cloudscraper_helper.post(
                self.api_url,
                json=query,
                headers=headers,
                timeout=90
            )
            
            if response.status_code == 200:
                data = response.json()

                # Check for GraphQL errors (including rate limit in response body)
                if 'errors' in data:
                    # Check if it's a rate limit error like PHP version
                    for error in data['errors']:
                        if isinstance(error, dict) and error.get('error') == 'rate_limit_reached':
                            reset_in = error.get('details', {}).get('reset_in', 'unknown')
                            raise Exception(f"Rate limit reached. Reset in {reset_in} seconds. Please wait and try again.")

                    self.logger.error(f"❌ GraphQL errors: {data['errors']}")
                    return None

                # Update rate limit info
                self._update_rate_limit_info(response.headers)

                return data

            elif response.status_code == 429:
                # Handle 429 rate limit like PHP version
                try:
                    data = response.json()
                    if 'errors' in data:
                        for error in data['errors']:
                            if isinstance(error, dict) and 'details' in error:
                                reset_in = error['details'].get('reset_in', 'unknown')
                                raise Exception(f"Rate limit reached. Reset in {reset_in} seconds. Please wait and try again.")
                except:
                    pass
                raise Exception("Rate limit reached. Please wait and try again.")

            elif response.status_code == 403:
                self.logger.error("❌ 403 Forbidden - Cloudflare protection not bypassed!")
                self.logger.error("   This should not happen with the working Firefox configuration")
                return None

            else:
                self.logger.error(f"❌ API request failed: {response.status_code}")
                self.logger.error(f"❌ Response: {response.text[:300]}...")
                return None
                
        except Exception as e:
            self.logger.error(f"GraphQL request failed: {e}")
            return None
    
    def _check_rate_limit(self) -> bool:
        """Check if we can make more API requests (like PHP version)"""
        if self.current_rate_limit is None:
            # No rate limit info yet, allow first request
            return True

        # Conservative check like PHP version (stop when <= 10 credits remaining)
        if self.current_rate_limit.remaining <= self.rate_limit_buffer:
            self.logger.warning(f"⚠️  Rate limit nearly exhausted: {self.current_rate_limit.remaining}/{self.current_rate_limit.limit} remaining")
            return False

        return True
    
    def _update_rate_limit_info(self, headers: Dict[str, str]):
        """Update rate limit information from response headers"""
        try:
            rate_limit = RateLimitInfo.from_headers(headers)
            self.current_rate_limit = rate_limit  # Store for _check_rate_limit()

            if rate_limit.remaining > 0:
                self.logger.rate_limit_info(rate_limit.remaining, rate_limit.limit)

                # Also output reset time info if available (for continuous_fetch.php parsing)
                if hasattr(rate_limit, 'reset_time') and rate_limit.reset_time:
                    # Calculate remaining time until reset (duration in seconds)
                    reset_in_seconds = max(0, int(rate_limit.reset_time.timestamp()) - int(time.time()))

                    # Format exactly like PHP: date('H:i:s', $reset_in_seconds)
                    # PHP's date() with duration shows it as HH:MM:SS from Unix epoch
                    # This means we format the duration seconds as time
                    from datetime import datetime, timezone
                    reset_duration_time = datetime.fromtimestamp(reset_in_seconds, tz=timezone.utc)
                    reset_time_str = reset_duration_time.strftime('%H:%M:%S')

                    # Output both formats like PHP script for continuous_fetch.php compatibility
                    print(f"🚦 Rate limit: {rate_limit.remaining}/{rate_limit.limit} remaining (resets at {reset_time_str}, reset_in: {reset_in_seconds} seconds)")

        except Exception as e:
            self.logger.debug(f"Could not parse rate limit info: {e}")
    
    def check_api_access(self) -> bool:
        """Test API access with a simple query (for --check-rate-limit)"""
        self.logger.info("🧪 Testing Product Hunt API access...")

        query = {
            'query': '''
                query testQuery {
                    viewer {
                        user {
                            id
                            name
                        }
                    }
                }
            '''
        }

        response = self._make_graphql_request(query)

        if response and 'data' in response:
            user_data = response.get('data', {}).get('viewer', {}).get('user', {})
            if user_data:
                print(f"✅ API access confirmed for user: {user_data.get('name', 'Unknown')}")
                # Output the expected message for continuous_fetch.php
                print("ℹ️ API credits available - no rate limit detected.")
                return True

        self.logger.error("❌ API access test failed")
        return False
