#!/usr/bin/env python3
"""
Configuration management for Product Hunt Python Scraper

This module handles loading configuration from multiple sources:
1. PHP config files (for database credentials)
2. Environment variables
3. JSON configuration files
"""

import os
import json
import re
from typing import Dict, Any, Optional
from dotenv import load_dotenv


class Settings:
    """Configuration management class"""
    
    def __init__(self):
        # Load environment variables from root .env file
        # Get the project root directory (3 levels up from this file)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        env_path = os.path.join(root_dir, '.env')
        load_dotenv(env_path)

        # Initialize configuration
        self._config = {}
        self._load_env_config()  # Load .env first (higher priority)
        self._load_php_config()  # PHP config as fallback
        self._load_json_config()
    
    def _load_php_config(self):
        """Load configuration from PHP config files"""
        try:
            # Load database configuration from PHP config
            php_config_path = os.path.join(os.path.dirname(__file__), '../../../config/config.php')
            if os.path.exists(php_config_path):
                with open(php_config_path, 'r') as f:
                    content = f.read()
                
                # Extract PHP constants using regex
                patterns = {
                    'DB_HOST': r"define\s*\(\s*['\"]DB_HOST['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                    'DB_NAME': r"define\s*\(\s*['\"]DB_NAME['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                    'DB_USER': r"define\s*\(\s*['\"]DB_USER['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                    'DB_PASSWORD': r"define\s*\(\s*['\"]DB_PASSWORD['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                    'PRODUCT_HUNT_API_KEY': r"define\s*\(\s*['\"]PRODUCT_HUNT_API_KEY['\"]\s*,\s*['\"]([^'\"]+)['\"]",
                }
                
                for key, pattern in patterns.items():
                    match = re.search(pattern, content, re.IGNORECASE)
                    if match:
                        self._config[key] = match.group(1)
                        
        except Exception as e:
            print(f"Warning: Could not load PHP config: {e}")
    
    def _load_env_config(self):
        """Load configuration from environment variables"""
        env_vars = [
            'DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD', 'PRODUCT_HUNT_API_KEY',
            'CLOUDSCRAPER_DEBUG', 'CLOUDSCRAPER_DELAY', 'CLOUDSCRAPER_MIN_DELAY',
            'CLOUDSCRAPER_MAX_DELAY', 'CLOUDSCRAPER_BROWSER', 'CLOUDSCRAPER_INTERPRETER',
            'LOG_LEVEL', 'LOG_FORMAT', 'DEFAULT_BATCH_SIZE', 'MAX_PAGES_DEFAULT',
            'RATE_LIMIT_BUFFER', 'HTTP_TIMEOUT', 'CONNECT_TIMEOUT', 'READ_TIMEOUT',
            'MAX_RETRIES', 'RETRY_DELAY', 'DEVELOPMENT_MODE', 'TEST_MODE'
        ]
        
        for var in env_vars:
            value = os.getenv(var)
            if value is not None:
                # Convert string values to appropriate types
                if var in ['CLOUDSCRAPER_DEBUG', 'DEVELOPMENT_MODE', 'TEST_MODE']:
                    self._config[var] = value.lower() in ('true', '1', 'yes', 'on')
                elif var in ['CLOUDSCRAPER_DELAY', 'CLOUDSCRAPER_MIN_DELAY', 'CLOUDSCRAPER_MAX_DELAY']:
                    self._config[var] = float(value)
                elif var in ['DEFAULT_BATCH_SIZE', 'MAX_PAGES_DEFAULT', 'RATE_LIMIT_BUFFER', 
                           'HTTP_TIMEOUT', 'CONNECT_TIMEOUT', 'READ_TIMEOUT', 'MAX_RETRIES', 'RETRY_DELAY']:
                    self._config[var] = int(value)
                else:
                    self._config[var] = value
    
    def _load_json_config(self):
        """Load configuration from JSON files"""
        try:
            # Load fetch schedule configuration
            schedule_path = os.path.join(os.path.dirname(__file__), '../../../config/fetch_schedule.json')
            if os.path.exists(schedule_path):
                with open(schedule_path, 'r') as f:
                    self._config['fetch_schedule'] = json.load(f)
        except Exception as e:
            print(f"Warning: Could not load JSON config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self._config.get(key, default)
    
    def get_database_config(self) -> Dict[str, str]:
        """Get database configuration"""
        return {
            'host': self.get('DB_HOST', 'localhost'),
            'database': self.get('DB_NAME', 'product_hunt'),
            'user': self.get('DB_USER', ''),
            'password': self.get('DB_PASSWORD', ''),
            'charset': 'utf8mb4'
        }
    
    def get_api_key(self) -> str:
        """Get Product Hunt API key"""
        return self.get('PRODUCT_HUNT_API_KEY', '')
    
    def get_cloudscraper_config(self) -> Dict[str, Any]:
        """Get cloudscraper configuration"""
        return {
            'debug': self.get('CLOUDSCRAPER_DEBUG', True),
            'delay': self.get('CLOUDSCRAPER_DELAY', 5),
            'min_delay': self.get('CLOUDSCRAPER_MIN_DELAY', 2.0),
            'max_delay': self.get('CLOUDSCRAPER_MAX_DELAY', 6.0),
            'browser': self.get('CLOUDSCRAPER_BROWSER', 'chrome'),
            'interpreter': self.get('CLOUDSCRAPER_INTERPRETER', 'js2py')
        }
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Get logging configuration"""
        return {
            'level': self.get('LOG_LEVEL', 'INFO'),
            'format': self.get('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        }
    
    def is_development_mode(self) -> bool:
        """Check if running in development mode"""
        return self.get('DEVELOPMENT_MODE', False)
    
    def is_test_mode(self) -> bool:
        """Check if running in test mode"""
        return self.get('TEST_MODE', False)


# Global settings instance
settings = Settings()
