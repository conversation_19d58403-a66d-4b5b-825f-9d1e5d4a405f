#!/usr/bin/env python3
"""
Product Hunt Products Fetcher

This script fetches products from Product Hunt GraphQL API using cloudscraper
to bypass Cloudflare protection. It's designed to replace the PHP version
and be called by continuous_fetch.php.

Usage:
    python fetch_products.py --date=2024-01-15
    python fetch_products.py --start-date=2024-01-01 --end-date=2024-01-31
    python fetch_products.py --check-rate-limit
"""

import sys
import os
import argparse
from datetime import datetime, date, timedelta
from typing import Optional

# Add src directory to Python path
src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
sys.path.insert(0, src_path)

# Add parent directory to Python path for package imports
parent_path = os.path.join(os.path.dirname(__file__), '..')
sys.path.insert(0, parent_path)

from src.fetchers.product_hunt_api import ProductHuntAPI
from src.utils.logging_helper import get_logger
from src.config.settings import settings
from src.config.database import db_config


def parse_date(date_str: str) -> date:
    """Parse date string in YYYY-MM-DD format"""
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except ValueError:
        raise ValueError(f"Invalid date format: {date_str}. Use YYYY-MM-DD format.")


def fetch_single_date(api: ProductHuntAPI, target_date: date, max_pages: int = 0) -> bool:
    """Fetch products for a single date"""
    logger = get_logger(__name__)

    try:
        total_products, completed = api.fetch_products_for_date(target_date, max_pages)
        
        if completed:
            print(f"✅ Successfully completed fetch for {target_date}: {total_products} products")
            return True
        else:
            print(f"⚠️  Partial fetch for {target_date}: {total_products} products (not completed)")
            return False
            
    except Exception as e:
        logger.error(f"❌ Failed to fetch products for {target_date}: {e}")
        return False


def fetch_date_range(api: ProductHuntAPI, start_date: date, end_date: date, max_pages: int = 0) -> bool:
    """Fetch products for a date range"""
    logger = get_logger(__name__)
    
    if start_date > end_date:
        logger.error("Start date must be before or equal to end date")
        return False
    
    logger.info(f"🚀 Fetching products from {start_date} to {end_date}")
    
    current_date = start_date
    success_count = 0
    total_days = (end_date - start_date).days + 1
    
    while current_date <= end_date:
        logger.info(f"\n📅 Processing date {current_date} ({success_count + 1}/{total_days})")
        
        if fetch_single_date(api, current_date, max_pages):
            success_count += 1
        
        current_date += timedelta(days=1)
    
    logger.info(f"\n📊 Date range fetch completed: {success_count}/{total_days} days successful")
    return success_count == total_days


def check_rate_limit(api: ProductHuntAPI) -> bool:
    """Check API rate limit status"""
    logger = get_logger(__name__)
    
    logger.info("🔍 Checking Product Hunt API rate limit...")
    
    if api.check_api_access():
        logger.info("✅ API access is working")
        return True
    else:
        logger.error("❌ API access failed")
        return False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Fetch products from Product Hunt API with Cloudflare bypass',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --date=2024-01-15
  %(prog)s --start-date=2024-01-01 --end-date=2024-01-31
  %(prog)s --check-rate-limit
  %(prog)s --date=2024-01-15 --max-pages=5
        """
    )
    
    # Date options (mutually exclusive) - not required, defaults to today like PHP version
    date_group = parser.add_mutually_exclusive_group(required=False)
    date_group.add_argument('--date', type=str,
                           help='Fetch products for specific date (YYYY-MM-DD, defaults to today)')
    date_group.add_argument('--start-date', type=str,
                           help='Start date for date range (YYYY-MM-DD)')
    date_group.add_argument('--check-rate-limit', action='store_true',
                           help='Check API rate limit status')
    
    # Additional options
    parser.add_argument('--end-date', type=str,
                       help='End date for date range (YYYY-MM-DD)')
    parser.add_argument('--max-pages', type=int, default=0,
                       help='Maximum pages to fetch (0 = unlimited)')
    
    args = parser.parse_args()

    # Set up logging
    logger = get_logger(__name__)
    logger.info("🚀 Product Hunt Python Fetcher Starting")
    print("ℹ️  Using Firefox + js2py configuration for Cloudflare bypass")
    
    try:
        # Test database connection
        if not db_config.test_connection():
            logger.error("❌ Database connection failed")
            return 1
        
        logger.info("✅ Database connection successful")
        
        # Initialize API client
        api = ProductHuntAPI()
        logger.info("✅ Product Hunt API client initialized")
        
        # Handle different command modes
        if args.check_rate_limit:
            success = check_rate_limit(api)
            return 0 if success else 1

        elif args.date:
            # Single date fetch
            target_date = parse_date(args.date)
            success = fetch_single_date(api, target_date, args.max_pages)
            return 0 if success else 1
        
        elif args.start_date:
            # Date range fetch
            if not args.end_date:
                logger.error("--end-date is required when using --start-date")
                return 1
            
            start_date = parse_date(args.start_date)
            end_date = parse_date(args.end_date)
            success = fetch_date_range(api, start_date, end_date, args.max_pages)
            return 0 if success else 1
        
        else:
            # Default to today's date (like PHP version)
            from datetime import date as date_class
            target_date = date_class.today()
            logger.info(f"📅 No date specified, defaulting to today: {target_date}")
            success = fetch_single_date(api, target_date, args.max_pages)
            return 0 if success else 1
    
    except KeyboardInterrupt:
        logger.warning("\n⚠️  Fetch interrupted by user")
        return 1
    
    except ValueError as e:
        logger.error(f"❌ Invalid input: {e}")
        return 1
    
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1
    
    finally:
        # Clean up database connection
        try:
            db_config.close()
        except:
            pass


if __name__ == "__main__":
    sys.exit(main())
