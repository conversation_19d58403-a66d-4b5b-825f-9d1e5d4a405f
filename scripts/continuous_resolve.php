<?php
/**
 * Continuous URL Resolving Daemon
 * Runs continuously to resolve URLs with these rules:
 * - Processes URLs with url_checked=null and external_url IS NULL
 * - Orders by updated_at DESC (newest first)
 * - Processes 50 URLs at a time
 * - Waits 6-12 seconds between requests
 * - Sets url_checked=1 after each attempt
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/url_utils.php';
require_once __DIR__ . '/../includes/slack_notifier.php';

class ContinuousURLResolver {
    private $running = true;
    private $batchSize = 50;
    private $logFile;
    private $slackNotifier;
    private $consecutiveErrors = 0;
    private $maxConsecutiveErrors = 10;

    public function __construct() {
        $this->logFile = __DIR__ . '/../logs/continuous_resolve.log';
        $this->ensureLogDirectory();
        $this->slackNotifier = new SlackNotifier();
        
        // Handle signals for graceful shutdown
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);
        }
    }
    
    private function ensureLogDirectory() {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        echo $logMessage;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    public function handleSignal($signal) {
        $this->log("Received signal $signal, shutting down gracefully...");
        $this->running = false;
    }
    
    public function run() {
        $this->log("🔗 Continuous URL Resolver started");

        while ($this->running) {
            try {
                if (function_exists('pcntl_signal_dispatch')) {
                    pcntl_signal_dispatch();
                }

                if (!$this->running) {
                    break; // Exit immediately if signal received
                }

                $this->processURLBatch();

                if (!$this->running) {
                    break; // Exit immediately if stopped during processing
                }

                // Small pause between batches, but check for signals
                for ($i = 0; $i < 5 && $this->running; $i++) {
                    sleep(1);
                    if (function_exists('pcntl_signal_dispatch')) {
                        pcntl_signal_dispatch();
                    }
                }

            } catch (Exception $e) {
                $this->log("❌ Error in resolve cycle: " . $e->getMessage());
                if ($this->running) {
                    sleep(30); // Wait 30 seconds before retrying
                }
            }
        }

        $this->log("🛑 Continuous URL Resolver stopped");
    }
    
    private function processURLBatch() {
        // Use the Python resolve_urls.py script for advanced cloudscraper functionality
        $pythonDir = __DIR__ . '/../python';
        $scriptPath = "$pythonDir/scripts/resolve_urls.py";

        // Use virtual environment if available, fallback to system python3
        $venvPython = "$pythonDir/venv/bin/python";
        $pythonCmd = file_exists($venvPython) ? $venvPython : 'python3';

        $command = "$pythonCmd $scriptPath --limit={$this->batchSize}";

        $this->log("🔧 Running: $command");

        // Use popen for real-time output
        $process = popen($command . ' 2>&1', 'r');
        if (!$process) {
            $this->log("❌ Failed to start URL resolver process");
            $this->consecutiveErrors++;
            return;
        }

        $output = [];
        $hasOutput = false;

        // Read output line by line in real-time
        while (($line = fgets($process)) !== false) {
            // Check for signals more frequently
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }

            if (!$this->running) {
                pclose($process);
                return;
            }

            $line = trim($line);
            if ($line) {
                $this->log("   $line");
                $output[] = $line;
                $hasOutput = true;
            }
        }

        $returnCode = pclose($process);

        if ($returnCode !== 0) {
            $this->consecutiveErrors++;
            $this->log("⚠️  URL resolver returned code: $returnCode (consecutive errors: {$this->consecutiveErrors})");

            // Check if we've hit the consecutive error limit
            if ($this->consecutiveErrors >= $this->maxConsecutiveErrors) {
                $this->handleConsecutiveErrors();
                return;
            }

            sleep(15); // Wait 15 seconds before retrying on error
        } elseif (!$hasOutput || strpos(implode(' ', $output), 'No products found') !== false) {
            $this->consecutiveErrors = 0; // Reset error count on success
            $this->log("💤 No URLs to resolve, sleeping for 15 seconds...");
            sleep(15);
        } else {
            $this->consecutiveErrors = 0; // Reset error count on success
            $this->log("✅ URL resolution batch completed");
            // Small pause between batches
            sleep(5);
        }
    }

    private function handleConsecutiveErrors() {
        $this->running = false;
        $errorMessage = "🚨 Continuous URL Resolver stopped due to {$this->consecutiveErrors} consecutive errors";

        $this->log($errorMessage);

        // Send Slack notification
        $this->slackNotifier->critical($errorMessage, [
            'consecutive_errors' => $this->consecutiveErrors,
            'max_allowed_errors' => $this->maxConsecutiveErrors,
            'script' => 'continuous_resolve.php',
            'action_required' => 'Check logs and restart the URL resolver'
        ]);

        $this->log("📱 Slack notification sent");
    }
}

// Run the daemon if this file is executed directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    $resolver = new ContinuousURLResolver();
    $resolver->run();
}
?>
