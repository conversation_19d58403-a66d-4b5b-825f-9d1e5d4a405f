<?php
/**
 * Continuous Product Fetching Daemon
 * Runs continuously to fetch products based on time rules:
 * - Fetches today's products every 6 hours
 * - When today is up to date, fetches historical data from yesterday backwards
 * - Handles rate limiting by waiting for reset time
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/slack_notifier.php';

class ContinuousFetcher {
    private $pdo;
    private $running = true;
    private $logFile;
    private $config;
    private $slackNotifier;
    private $consecutiveRateLimits = 0;
    private $maxConsecutiveRateLimits = 3;
    private $hasWaitedForRateLimit = false;

    // Simple rate limit tracking
    private $creditsRemaining = null;
    private $resetTime = null;
    private $creditsPerCall = 100; // Each API call costs 100 credits
    private $lastRateLimitInfo = null; // For compatibility with old methods
    
    public function __construct() {
        try {
            $this->pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASSWORD,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );

            $this->logFile = __DIR__ . '/../logs/continuous_fetch.log';
            $this->ensureLogDirectory();
            $this->loadConfig();
            $this->slackNotifier = new SlackNotifier();
            
        } catch (PDOException $e) {
            $this->log("Database connection failed: " . $e->getMessage());
            exit(1);
        }
        
        // Handle signals for graceful shutdown
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, [$this, 'handleSignal']);
            pcntl_signal(SIGINT, [$this, 'handleSignal']);
        }
    }
    
    private function ensureLogDirectory() {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    private function loadConfig() {
        $configFile = __DIR__ . '/../config/fetch_schedule.json';
        if (!file_exists($configFile)) {
            $this->log("⚠️  Configuration file not found, using defaults");
            $this->config = $this->getDefaultConfig();
            return;
        }

        $configContent = file_get_contents($configFile);
        $this->config = json_decode($configContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log("⚠️  Invalid JSON in configuration file, using defaults");
            $this->config = $this->getDefaultConfig();
            return;
        }

        $this->log("✅ Configuration loaded successfully");
    }

    private function getDefaultConfig() {
        return [
            'settings' => [
                'timezone' => 'America/Los_Angeles',
                'check_interval_minutes' => 5,
                'rate_limit_buffer_seconds' => 15
            ],
            'daily_schedule' => [
                ['time' => '00:15', 'target' => 'today', 'description' => 'Fetch today\'s products at 12:15 AM PT'],
                ['time' => '06:00', 'target' => 'today', 'description' => 'Fetch today\'s products at 6:00 AM PT'],
                ['time' => '12:00', 'target' => 'today', 'description' => 'Fetch today\'s products at 12:00 PM PT'],
                ['time' => '18:00', 'target' => 'today', 'description' => 'Fetch today\'s products at 6:00 PM PT'],
                ['time' => '23:45', 'target' => 'today', 'description' => 'Fetch today\'s products at 11:45 PM PT']
            ],
            'targets' => [
                'today' => ['description' => 'Today\'s products (PT timezone)', 'strategy' => 'fetch_current_date'],
                'historical' => ['description' => 'Historical backfill', 'strategy' => 'fetch_next_historical_date', 'direction' => 'backward', 'stop_at' => '2013-01-01']
            ]
        ];
    }
    
    private function log($message) {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message\n";
        echo $logMessage;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
    
    public function handleSignal($signal) {
        $this->log("Received signal $signal, shutting down gracefully...");
        $this->running = false;
    }
    
    public function run() {
        $this->log("🚀 Continuous Product Fetcher started");

        while ($this->running) {
            try {
                if (function_exists('pcntl_signal_dispatch')) {
                    pcntl_signal_dispatch();
                }

                if (!$this->running) {
                    break; // Exit immediately if signal received
                }

                $this->processFetchCycle();

                if (!$this->running) {
                    break; // Exit immediately if stopped during processing
                }

            } catch (Exception $e) {
                $this->log("❌ Error in fetch cycle: " . $e->getMessage());
                if ($this->running) {
                    sleep(60); // Wait 1 minute before retrying
                }
            }
        }

        $this->log("🛑 Continuous Product Fetcher stopped");
    }
    
    private function processFetchCycle() {
        // Get current time in Pacific timezone
        $timezone = new DateTimeZone($this->config['settings']['timezone'] ?? 'America/Los_Angeles');
        $now = new DateTime('now', $timezone);
        $currentTime = $now->format('H:i');

        // Find the appropriate schedule rule
        $scheduleRule = $this->findScheduleRule($currentTime);

        if ($scheduleRule) {
            $this->log("🕐 Current time: $currentTime PT - Running schedule: " . $scheduleRule['description']);
            $this->executeScheduleRule($scheduleRule);

            // After successful schedule execution, check rate limits and continue if possible
            $this->smartSleep('schedule');
        } else {
            // No schedule rule matches - check if we should do historical fetching
            if ($this->shouldDoHistoricalFetch()) {
                $this->log("⏰ Current time: $currentTime PT - No schedule rule matches, checking historical data");
                $this->executeHistoricalFetch();

                // After historical fetch, check rate limits and continue if possible
                $this->smartSleep('historical');
            } else {
                $this->log("⏰ Current time: $currentTime PT - No schedule rule matches, no historical work needed");

                // Nothing to do, sleep 1 minute
                $this->log("💤 Nothing to do, sleeping for 1 minute...");
                $this->interruptibleSleep(60);
            }
        }
    }

    private function findScheduleRule($currentTime) {
        $schedules = $this->config['daily_schedule'] ?? [];

        // Convert current time to minutes for comparison
        list($currentHour, $currentMinute) = explode(':', $currentTime);
        $currentMinutes = ($currentHour * 60) + $currentMinute;

        // Find the EARLIEST unrun schedule that should have run TODAY
        $bestMatch = null;
        $bestMatchMinutes = 9999; // Start with high value to find the EARLIEST unrun schedule

        foreach ($schedules as $schedule) {
            list($scheduleHour, $scheduleMinute) = explode(':', $schedule['time']);
            $scheduleMinutes = ($scheduleHour * 60) + $scheduleMinute;

            // Only consider schedules that have already passed today (not future schedules)
            if ($scheduleMinutes <= $currentMinutes && $scheduleMinutes < $bestMatchMinutes) {
                // Check if this schedule hasn't been run today
                if ($this->shouldRunSchedule($schedule)) {
                    $bestMatch = $schedule;
                    $bestMatchMinutes = $scheduleMinutes;
                }
            }
        }

        return $bestMatch;
    }

    private function shouldRunSchedule($schedule) {
        // Check if this schedule has been run today
        $timezone = new DateTimeZone($this->config['settings']['timezone'] ?? 'America/Los_Angeles');
        $today = new DateTime('now', $timezone);
        $todayStart = $today->format('Y-m-d 00:00:00');

        $stmt = $this->pdo->prepare("
            SELECT id FROM schedule_logs
            WHERE schedule_time = ?
            AND target_type = ?
            AND started_at >= ?
            AND status = 'success'
            ORDER BY started_at DESC
            LIMIT 1
        ");
        $stmt->execute([$schedule['time'], $schedule['target'], $todayStart]);

        return $stmt->rowCount() === 0;
    }

    private function executeScheduleRule($rule) {
        $logId = $this->logScheduleStart($rule);

        try {
            if ($rule['target'] === 'today') {
                $this->log("📅 Fetching today's products...");
                $result = $this->runFetchScript();
            } elseif ($rule['target'] === 'historical') {
                $nextDate = $this->getNextHistoricalDate();
                if ($nextDate) {
                    $this->log("📚 Fetching historical data for: $nextDate");
                    $result = $this->runFetchScript(['--date' => $nextDate]);
                } else {
                    $this->log("✅ All historical data is up to date");
                    $result = ['success' => true, 'products_fetched' => 0];
                }
            } else {
                throw new Exception("Unknown target type: " . $rule['target']);
            }

            $this->logScheduleComplete($logId, $result);

        } catch (Exception $e) {
            $this->logScheduleError($logId, $e->getMessage());
        }
    }

    private function logScheduleStart($rule) {
        $targetDate = null;
        if ($rule['target'] === 'today') {
            $timezone = new DateTimeZone($this->config['settings']['timezone'] ?? 'America/Los_Angeles');
            $now = new DateTime('now', $timezone);
            $targetDate = $now->format('Y-m-d');
        } elseif ($rule['target'] === 'historical') {
            $targetDate = $this->getNextHistoricalDate();
        }

        $stmt = $this->pdo->prepare("
            INSERT INTO schedule_logs (schedule_time, target_type, target_date, status)
            VALUES (?, ?, ?, 'running')
        ");
        $stmt->execute([$rule['time'], $rule['target'], $targetDate]);

        return $this->pdo->lastInsertId();
    }

    private function logScheduleComplete($logId, $result) {
        $status = ($result['success'] ?? false) ? 'success' : 'failed';
        $productsFetched = $result['products_fetched'] ?? 0;

        $stmt = $this->pdo->prepare("
            UPDATE schedule_logs
            SET status = ?, products_fetched = ?, completed_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$status, $productsFetched, $logId]);
    }

    private function logScheduleError($logId, $errorMessage) {
        $stmt = $this->pdo->prepare("
            UPDATE schedule_logs
            SET status = 'error', error_message = ?, completed_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$errorMessage, $logId]);
    }

    private function shouldDoHistoricalFetch() {
        // Check if there's any historical work to do
        $nextDate = $this->getNextHistoricalDate();
        if (!$nextDate) {
            return false; // No more historical dates to fetch
        }

        // Always allow historical fetching (no cooldown)
        return true;
    }

    private function executeHistoricalFetch() {
        $nextDate = $this->getNextHistoricalDate();
        if (!$nextDate) {
            $this->log("✅ All historical data is up to date");
            return;
        }

        // Create a pseudo-schedule rule for historical fetching
        $rule = [
            'time' => 'auto',
            'target' => 'historical',
            'description' => "Automatic historical fetch for $nextDate"
        ];

        $logId = $this->logScheduleStart($rule);

        try {
            $this->log("📚 Fetching historical data for: $nextDate");
            $result = $this->runFetchScript(['--date' => $nextDate]);
            $this->logScheduleComplete($logId, $result);
        } catch (Exception $e) {
            $this->logScheduleError($logId, $e->getMessage());
        }
    }

    private function smartSleep($context) {
        // Always sleep 15 seconds first
        $this->log("💤 Sleeping for 15 seconds...");
        $this->interruptibleSleep(15);

        if (!$this->running) {
            return; // Exit if stopped during sleep
        }

        // Check database for current rate limit status
        $this->loadRateLimitFromDatabase();

        // Simple rate limit check - do we have enough credits for the next call?
        if (!$this->hasEnoughCredits()) {
            $this->waitForRateReset();
            return;
        }

        $this->log("✅ Sufficient API credits available - continuing");
    }

    private function hasEnoughCredits() {
        // If we don't know our credit status, assume we need to check
        if ($this->creditsRemaining === null) {
            return true; // Let the fetch script handle the first check
        }

        // If reset time has passed, we have full credits again
        if ($this->resetTime && time() >= $this->resetTime) {
            $this->log("✅ Rate limit reset time has passed, credits restored");
            $this->creditsRemaining = 6250; // Full credits
            $this->resetTime = null;
            return true;
        }

        // Simple math: do we have enough credits for the next call? (with safety buffer)
        $needed = $this->creditsPerCall + 200; // Add 200-credit safety buffer
        if ($this->creditsRemaining >= $needed) {
            return true;
        }

        $this->log("⚠️  Insufficient credits: {$this->creditsRemaining} remaining, need {$this->creditsPerCall} + 200 buffer = $needed");
        return false;
    }

    private function waitForRateReset() {
        if (!$this->resetTime) {
            $this->log("⏳ No reset time available, waiting 15 minutes");
            $this->interruptibleSleep(900); // 15 minutes
            // Assume credits are restored after generic wait
            $this->creditsRemaining = 6250;
            $this->resetTime = null;
            $this->saveRateLimitToDatabase();
            return;
        }

        $waitTime = $this->resetTime - time();
        if ($waitTime <= 0) {
            $this->log("✅ Reset time has already passed");
            $this->creditsRemaining = 6250;
            $this->resetTime = null;
            $this->saveRateLimitToDatabase();
            return;
        }

        $minutes = round($waitTime / 60, 1);
        $this->log("⏳ Waiting $waitTime seconds ($minutes minutes) for rate limit reset");
        $this->interruptibleSleep($waitTime + 15); // Add 15 second buffer

        // Reset credits after waiting
        $this->creditsRemaining = 6250;
        $this->resetTime = null;
        $this->saveRateLimitToDatabase();
    }

    private function updateCreditsFromFetchResult($output) {
        $updated = false;

        // Extract rate limit info from fetch script output
        // Priority 1: Look for 🚦 Rate limit (from actual API calls) - most accurate
        if (preg_match_all('/🚦 Rate limit: (\d+)\/(\d+) remaining/', $output, $matches, PREG_SET_ORDER)) {
            $lastMatch = end($matches);
            $newCredits = intval($lastMatch[1]);

            if ($this->creditsRemaining !== null && $this->creditsRemaining !== $newCredits) {
                $this->log("🔄 Credits changed: {$this->creditsRemaining} → $newCredits (from API call)");
            }

            $this->creditsRemaining = $newCredits;
            $this->log("📊 Credits updated: {$this->creditsRemaining} remaining (from API call)");
            $updated = true;
        }
        // Priority 2: Fallback to any Rate limit pattern (but only if no API call data found)
        elseif (preg_match_all('/Rate limit: (\d+)\/(\d+) remaining/', $output, $matches, PREG_SET_ORDER)) {
            $lastMatch = end($matches);
            $newCredits = intval($lastMatch[1]);

            if ($this->creditsRemaining !== null && $this->creditsRemaining !== $newCredits) {
                $this->log("🔄 Credits changed: {$this->creditsRemaining} → $newCredits (from rate limit check)");
            }

            $this->creditsRemaining = $newCredits;
            $this->log("📊 Credits updated: {$this->creditsRemaining} remaining (from rate limit check)");
            $updated = true;
        }

        // Extract reset time from "resets at HH:MM:SS" format - this is a duration, not clock time
        if (preg_match('/resets at (\d{2}):(\d{2}):(\d{2})/', $output, $matches)) {
            $hours = intval($matches[1]);
            $minutes = intval($matches[2]);
            $seconds = intval($matches[3]);

            // Convert duration to total seconds
            $totalSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;

            // Set reset time as current time + duration
            $this->resetTime = time() + $totalSeconds;

            $this->log("🕒 Rate limit resets in {$totalSeconds} seconds ({$hours}h {$minutes}m {$seconds}s)");
            $updated = true;
        }

        // Save to database if anything was updated
        if ($updated) {
            $this->saveRateLimitToDatabase();
        }
    }

    private function loadRateLimitFromDatabase() {
        try {
            // Reconnect if connection is dead
            $this->ensureDatabaseConnection();

            $stmt = $this->pdo->prepare("SELECT credits_remaining, reset_time FROM rate_limit_status ORDER BY id DESC LIMIT 1");
            $stmt->execute();
            $row = $stmt->fetch();

            if ($row) {
                $this->creditsRemaining = intval($row['credits_remaining']);

                if ($row['reset_time']) {
                    $resetTimestamp = strtotime($row['reset_time']);

                    // If reset time has passed, clear it and restore full credits
                    if ($resetTimestamp <= time()) {
                        $this->creditsRemaining = 6250;
                        $this->resetTime = null;
                        $this->log("✅ Rate limit reset time has passed, credits restored to 6250");
                        $this->saveRateLimitToDatabase(); // Update database
                    } else {
                        $this->resetTime = $resetTimestamp;
                        $waitTime = $this->resetTime - time();
                        $this->log("📊 Loaded from database: {$this->creditsRemaining} credits, resets in {$waitTime} seconds");
                    }
                } else {
                    $this->resetTime = null;
                    $this->log("📊 Loaded from database: {$this->creditsRemaining} credits, no reset time");
                }
            }
        } catch (PDOException $e) {
            $this->log("⚠️  Error loading rate limit from database: " . $e->getMessage());
        }
    }

    private function saveRateLimitToDatabase() {
        try {
            // Reconnect if connection is dead
            $this->ensureDatabaseConnection();

            $resetTimeForDb = $this->resetTime ? date('Y-m-d H:i:s', $this->resetTime) : null;

            $stmt = $this->pdo->prepare("
                UPDATE rate_limit_status
                SET credits_remaining = ?, reset_time = ?, last_updated = CURRENT_TIMESTAMP
                ORDER BY id DESC LIMIT 1
            ");
            $stmt->execute([$this->creditsRemaining, $resetTimeForDb]);

            $this->log("💾 Rate limit status saved to database");
        } catch (PDOException $e) {
            $this->log("⚠️  Error saving rate limit to database: " . $e->getMessage());
        }
    }

    private function ensureDatabaseConnection() {
        try {
            // Test if connection is alive
            $this->pdo->query('SELECT 1');
        } catch (PDOException $e) {
            // Connection is dead, reconnect
            $this->log("🔄 Database connection lost, reconnecting...");
            $this->pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASSWORD,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]
            );
            $this->log("✅ Database connection restored");
        }
    }

    private function checkRateLimitStatus() {
        // Make a lightweight API call to check rate limit status
        $pythonDir = __DIR__ . '/../python';
        $scriptPath = "$pythonDir/scripts/fetch_products.py";

        // Use virtual environment if available, fallback to system python3
        $venvPython = "$pythonDir/venv/bin/python";
        $pythonCmd = file_exists($venvPython) ? $venvPython : 'python3';

        $command = "$pythonCmd $scriptPath --check-rate-limit 2>&1";

        $output = shell_exec($command);
        $output = trim($output);

        $this->log("🔍 Rate limit check output: $output");

        // Default to optimistic approach - if we can't determine, assume we have credits
        $result = [
            'has_credits' => true,
            'not_rate_limited' => true,
            'rate_limited' => false,
            'reset_time' => 0,
            'remaining_credits' => null
        ];

        if ($output) {
            // Check for explicit rate limit messages
            if (strpos($output, 'Rate limit reached') !== false) {
                $result['rate_limited'] = true;
                $result['not_rate_limited'] = false;
                $result['has_credits'] = false;

                // Try to extract reset time
                if (preg_match('/Reset in (\d+) seconds/', $output, $matches)) {
                    $result['reset_time'] = intval($matches[1]);
                } elseif (preg_match('/Try again in (\d+) seconds/', $output, $matches)) {
                    $result['reset_time'] = intval($matches[1]);
                } elseif (preg_match('/resets in (\d+) seconds/', $output, $matches)) {
                    $result['reset_time'] = intval($matches[1]);
                } else {
                    // Default wait time if no specific time found
                    $result['reset_time'] = 300; // 5 minutes
                }
            } elseif (strpos($output, 'API credits available') !== false) {
                // Explicitly confirmed we have credits
                $result['has_credits'] = true;
                $result['not_rate_limited'] = true;
                $result['rate_limited'] = false;

                // Extract remaining credits: "Rate limit: 4750/6250 remaining"
                if (preg_match('/Rate limit: (\d+)\/(\d+) remaining/', $output, $matches)) {
                    $result['remaining_credits'] = intval($matches[1]);
                }
                $result['reset_time'] = 0;

                // Extract rate limit info for logging if available
                if (preg_match('/Rate limit: (\d+)\/(\d+) remaining/', $output, $matches)) {
                    $remaining = intval($matches[1]);
                    $limit = intval($matches[2]);
                    $this->log("📊 API Status: $remaining/$limit credits remaining");
                }

                // Extract reset time if available: "reset_in: 123 seconds"
                if (preg_match('/reset_in: (\d+) seconds/', $output, $matches)) {
                    $resetInSeconds = intval($matches[1]);
                    $result['reset_time'] = $resetInSeconds;
                    $this->log("🕒 Rate limit resets in $resetInSeconds seconds");
                }
            } elseif (strpos($output, 'API error') !== false || strpos($output, 'failed') !== false) {
                // API error - be conservative but don't assume rate limited
                $result['has_credits'] = false;
                $result['not_rate_limited'] = false;
                $result['rate_limited'] = false; // Unknown, not necessarily rate limited
                $result['reset_time'] = 60; // Short wait for API errors
            }
        }

        return $result;
    }

    private function interruptibleSleep($seconds) {
        $sleepInterval = 1; // Check every 1 second for faster response

        for ($i = 0; $i < $seconds && $this->running; $i += $sleepInterval) {
            sleep(min($sleepInterval, $seconds - $i));
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }
            // Exit immediately if signal received
            if (!$this->running) {
                break;
            }
        }
    }


    
    private function getNextHistoricalDate() {
        // Check both schedule_logs and fetch_progress to find the next date to fetch

        // First, get all completed dates from both tables
        $stmt = $this->pdo->prepare("
            SELECT DISTINCT target_date
            FROM (
                SELECT target_date
                FROM schedule_logs
                WHERE target_type = 'historical'
                AND status = 'success'
                AND target_date IS NOT NULL

                UNION

                SELECT target_date
                FROM fetch_progress
                WHERE status = 'completed'
                AND target_date IS NOT NULL
            ) AS completed_dates
            ORDER BY target_date DESC
        ");
        $stmt->execute();
        $completedDates = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // Find the earliest date that hasn't been completed
        $checkDate = date('Y-m-d', strtotime('-1 day')); // Start from yesterday

        while ($checkDate >= '2013-01-01') {
            if (!in_array($checkDate, $completedDates)) {
                // Check if there's an in-progress or rate-limited fetch for this date
                $stmt = $this->pdo->prepare("
                    SELECT status FROM fetch_progress
                    WHERE target_date = ?
                    AND status IN ('in_progress', 'rate_limited')
                ");
                $stmt->execute([$checkDate]);
                $inProgress = $stmt->fetch();

                if ($inProgress) {
                    // This date has incomplete fetch, return it for resumption
                    $this->log("📋 Found incomplete fetch for $checkDate (status: {$inProgress['status']})");
                    return $checkDate;
                } else {
                    // This date hasn't been fetched at all, return it
                    return $checkDate;
                }
            }

            // This date is completed, check the previous day
            $checkDate = date('Y-m-d', strtotime($checkDate . ' -1 day'));
        }

        // No more dates to fetch (reached 2013)
        return null;
    }
    
    private function runFetchScript($params = []) {
        $pythonDir = __DIR__ . '/../python';
        $scriptPath = "$pythonDir/scripts/fetch_products.py";

        // Use virtual environment if available, fallback to system python3
        $venvPython = "$pythonDir/venv/bin/python";
        $pythonCmd = file_exists($venvPython) ? $venvPython : 'python3';

        $command = "$pythonCmd $scriptPath";

        // Load current rate limit status from database first
        $this->loadRateLimitFromDatabase();

        // Simple credit check - do we have enough for at least one call?
        if (!$this->hasEnoughCredits()) {
            $this->log("🚫 Skipping fetch due to insufficient API credits");
            return ['success' => false, 'error' => 'Insufficient API credits'];
        }

        // Calculate safe page limit based on available credits with safety buffer
        $safePageLimit = 1; // Start with 1 page minimum
        if ($this->creditsRemaining !== null) {
            // Leave a 200-credit safety buffer to avoid hitting rate limits
            $availableCredits = max(0, $this->creditsRemaining - 200);
            $safePageLimit = max(1, floor($availableCredits / $this->creditsPerCall));
            $this->log("📊 Safe page limit: $safePageLimit pages ({$this->creditsRemaining} credits available, {$availableCredits} usable with buffer)");
        }

        // Add safe page limit to parameters (unless already specified)
        if (!isset($params['--max-pages'])) {
            $params['--max-pages'] = $safePageLimit;
        }

        foreach ($params as $key => $value) {
            $command .= " $key=$value";
        }

        $this->log("🔧 Running: $command");

        // Use popen for real-time output
        $process = popen($command . ' 2>&1', 'r');
        if (!$process) {
            $this->log("❌ Failed to start fetch process");
            return ['success' => false, 'error' => 'Failed to start process'];
        }

        $output = [];

        // Read output line by line in real-time
        while (($line = fgets($process)) !== false) {
            // Check for signals more frequently
            if (function_exists('pcntl_signal_dispatch')) {
                pcntl_signal_dispatch();
            }

            if (!$this->running) {
                pclose($process);
                return ['success' => false, 'error' => 'Process interrupted'];
            }

            $line = trim($line);
            if ($line) {
                $this->log("   $line");
                $output[] = $line;
            }
        }

        $returnCode = pclose($process);

        // Update our credit tracking from the fetch output
        $outputText = implode(' ', $output);
        $this->updateCreditsFromFetchResult($outputText);

        $result = ['success' => false, 'products_fetched' => 0];

        if ($returnCode !== 0) {
            $this->log("⚠️  Fetch script returned code: $returnCode");

            // Check if it's a rate limit error
            $outputText = implode(' ', $output);
            if (strpos($outputText, 'Rate limit reached') !== false ||
                strpos($outputText, 'rate limit') !== false ||
                strpos($outputText, 'HTTP code: 429') !== false) {
                $this->handleRateLimit($outputText);
                $result['error'] = 'Rate limit reached';
            } else {
                $result['error'] = 'Script failed with code ' . $returnCode;
            }
        } else {
            $this->log("✅ Fetch completed successfully");
            $result['success'] = true;

            // Reset rate limit tracking on successful fetch
            $this->consecutiveRateLimits = 0;
            $this->hasWaitedForRateLimit = false;

            // Try to extract products count from output
            $outputText = implode(' ', $output);
            if (preg_match('/(\d+) products/', $outputText, $matches)) {
                $result['products_fetched'] = intval($matches[1]);
            }
        }

        return $result;
    }
    
    private function handleRateLimit($output) {
        // Track consecutive rate limits
        if ($this->hasWaitedForRateLimit) {
            $this->consecutiveRateLimits++;
            $this->log("⚠️  Consecutive rate limit #{$this->consecutiveRateLimits} after waiting");

            // Check if we've hit too many consecutive rate limits
            if ($this->consecutiveRateLimits >= $this->maxConsecutiveRateLimits) {
                $this->handleConsecutiveRateLimits();
                return;
            }
        } else {
            $this->hasWaitedForRateLimit = true;
            $this->consecutiveRateLimits = 1;
        }

        // Try to extract reset time from output - check multiple patterns
        $resetInSeconds = null;
        $bufferSeconds = $this->config['settings']['rate_limit_buffer_seconds'] ?? 15;

        // Pattern 1: JSON response with "reset_in" field
        if (preg_match('/"reset_in":(\d+)/', $output, $matches)) {
            $resetInSeconds = intval($matches[1]);
            $this->log("🔍 Found reset_in from JSON: {$resetInSeconds} seconds");
        }
        // Pattern 2: "Reset in X seconds"
        elseif (preg_match('/Reset in (\d+) seconds/', $output, $matches)) {
            $resetInSeconds = intval($matches[1]);
        }
        // Pattern 3: "Try again in X seconds"
        elseif (preg_match('/Try again in (\d+) seconds/', $output, $matches)) {
            $resetInSeconds = intval($matches[1]);
        }
        // Pattern 4: "Rate limit resets in X seconds"
        elseif (preg_match('/resets in (\d+) seconds/', $output, $matches)) {
            $resetInSeconds = intval($matches[1]);
        }
        // Pattern 5: Look for any number followed by "seconds" in rate limit context
        elseif (preg_match('/(\d+)\s*seconds?/i', $output, $matches)) {
            $seconds = intval($matches[1]);
            // Only use if it's a reasonable wait time (between 1 minute and 2 hours)
            if ($seconds >= 60 && $seconds <= 7200) {
                $resetInSeconds = $seconds;
            }
        }

        if ($resetInSeconds) {
            // Store rate limit info for future use (survives script restarts)
            $resetTime = time() + $resetInSeconds;
            $this->storeRateLimitInfo($resetTime);

            $waitTime = $resetInSeconds + $bufferSeconds;
            $minutes = round($waitTime / 60, 1);
            $this->log("⏳ Rate limit hit. Waiting $waitTime seconds ($minutes minutes) for reset...");
            $this->interruptibleSleep($waitTime);
        } else {
            // Default wait time if we can't parse the reset time
            $this->log("⏳ Rate limit hit. No reset time found, waiting 30 minutes...");
            $this->interruptibleSleep(1800);
        }
    }

    private function handleConsecutiveRateLimits() {
        $this->running = false;
        $errorMessage = "🚨 Continuous Fetcher stopped due to {$this->consecutiveRateLimits} consecutive rate limits after waiting";

        $this->log($errorMessage);

        // Send Slack notification
        $this->slackNotifier->critical($errorMessage, [
            'consecutive_rate_limits' => $this->consecutiveRateLimits,
            'max_allowed_rate_limits' => $this->maxConsecutiveRateLimits,
            'script' => 'continuous_fetch.php',
            'action_required' => 'Check API key and rate limits, then restart the fetcher'
        ]);

        $this->log("📱 Slack notification sent");
    }

    private function calculateSafePageLimit() {
        // Use cached rate limit info if available to avoid double API calls
        $rateLimitInfo = $this->lastRateLimitInfo;

        // Only make API call if we don't have recent cached info
        if (!$rateLimitInfo || !isset($rateLimitInfo['remaining_credits'])) {
            $this->log("🔍 No cached rate limit info, checking API status...");
            $rateLimitInfo = $this->checkRateLimitStatus();
            $this->lastRateLimitInfo = $rateLimitInfo;
        }

        if (!$rateLimitInfo['has_credits']) {
            return 0; // No pages if no credits
        }

        // Use the rate limit info we already have
        $remaining = null;

        if (isset($rateLimitInfo['remaining_credits'])) {
            $remaining = $rateLimitInfo['remaining_credits'];
            $this->log("📊 Using cached API credits: $remaining");
        } else {
            // Fallback: make API call only if we don't have cached info
            $pythonDir = __DIR__ . '/../python';
            $scriptPath = "$pythonDir/scripts/fetch_products.py";

            // Use virtual environment if available, fallback to system python3
            $venvPython = "$pythonDir/venv/bin/python";
            $pythonCmd = file_exists($venvPython) ? $venvPython : 'python3';

            $command = "$pythonCmd $scriptPath --check-rate-limit 2>&1";
            $output = shell_exec($command);

            // Parse rate limit info: "Rate limit: 4750/6250 remaining"
            if (preg_match('/Rate limit: (\d+)\/(\d+) remaining/', $output, $matches)) {
                $remaining = intval($matches[1]);
                $limit = intval($matches[2]);
                $this->log("📊 API Credits from fresh check: $remaining/$limit remaining");
            }
        }

        if ($remaining === null) {
            // Conservative default if we can't determine credits
            $this->log("⚠️  Could not determine remaining credits, using conservative limit");
            return 5; // Conservative default
        }

        // Calculate safe page limit
        // Each GraphQL query costs ~100 credits (observed from logs)
        $creditsPerPage = 100; // Each API call costs ~100 credits
        $buffer = 200; // Keep 200 credits as buffer (2 API calls worth)
        $safeCredits = max(0, $remaining - $buffer);
        $safePages = min(floor($safeCredits / $creditsPerPage), 10); // Cap at 10 pages per batch

        if ($safePages <= 0) {
            $this->log("⚠️  Very low credits ($remaining remaining), skipping fetch");
            return 0;
        }

        $this->log("🎯 Safe page limit: $safePages pages (credits: $remaining, cost: $creditsPerPage/page, buffer: $buffer)");
        return $safePages;
    }

    private function getStoredRateLimitInfo() {
        // First check memory
        if ($this->lastRateLimitInfo) {
            return $this->lastRateLimitInfo;
        }

        // Then check database
        try {
            $stmt = $this->pdo->prepare("
                SELECT reset_time, stored_at
                FROM rate_limit_info
                WHERE id = 1 AND reset_time > UNIX_TIMESTAMP()
                ORDER BY stored_at DESC
                LIMIT 1
            ");
            $stmt->execute();
            $result = $stmt->fetch();

            if ($result) {
                return [
                    'reset_time' => intval($result['reset_time']),
                    'stored_at' => intval($result['stored_at'])
                ];
            }
        } catch (Exception $e) {
            // If table doesn't exist or other error, continue without stored info
            $this->log("⚠️  Could not retrieve stored rate limit info: " . $e->getMessage());
        }

        return null;
    }

    private function storeRateLimitInfo($resetTime) {
        $info = [
            'reset_time' => $resetTime,
            'stored_at' => time()
        ];

        // Store in memory
        $this->lastRateLimitInfo = $info;

        // Store in database
        try {
            // Create table if it doesn't exist
            $this->pdo->exec("
                CREATE TABLE IF NOT EXISTS rate_limit_info (
                    id INT PRIMARY KEY DEFAULT 1,
                    reset_time INT NOT NULL,
                    stored_at INT NOT NULL,
                    INDEX idx_reset_time (reset_time)
                ) ENGINE=InnoDB
            ");

            $stmt = $this->pdo->prepare("
                INSERT INTO rate_limit_info (id, reset_time, stored_at)
                VALUES (1, ?, ?)
                ON DUPLICATE KEY UPDATE reset_time = VALUES(reset_time), stored_at = VALUES(stored_at)
            ");
            $stmt->execute([$resetTime, time()]);

            $this->log("💾 Stored rate limit info: reset at " . date('H:i:s', $resetTime));
        } catch (Exception $e) {
            $this->log("⚠️  Could not store rate limit info: " . $e->getMessage());
        }
    }

    private function clearStoredRateLimitInfo() {
        // Clear from memory
        $this->lastRateLimitInfo = null;

        // Clear from database
        try {
            $this->pdo->exec("DELETE FROM rate_limit_info WHERE id = 1");
        } catch (Exception) {
            // Ignore errors when clearing
        }
    }
}

// Run the daemon if this file is executed directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    $fetcher = new ContinuousFetcher();
    $fetcher->run();
}
?>
