# =============================================================================
# Product Hunt Dashboard - Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
DB_NAME=product_hunt
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306

# -----------------------------------------------------------------------------
# Product Hunt API
# -----------------------------------------------------------------------------
PRODUCT_HUNT_API_KEY=your_api_key_here

# -----------------------------------------------------------------------------
# Web Interface Authentication
# -----------------------------------------------------------------------------
WEB_USER=admin
WEB_PASSWORD=your_secure_password

# -----------------------------------------------------------------------------
# Slack Notifications
# -----------------------------------------------------------------------------
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
SLACK_NOTIFICATIONS_ENABLED=true

# -----------------------------------------------------------------------------
# Python Backend Configuration
# -----------------------------------------------------------------------------

# Cloudscraper Settings
CLOUDSCRAPER_DEBUG=false
CLOUDSCRAPER_DELAY=5
CLOUDSCRAPER_MIN_DELAY=2.0
CLOUDSCRAPER_MAX_DELAY=6.0
CLOUDSCRAPER_BROWSER=chrome
CLOUDSCRAPER_INTERPRETER=js2py

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Rate Limiting & Batching
DEFAULT_BATCH_SIZE=20
MAX_PAGES_DEFAULT=0
RATE_LIMIT_BUFFER=100

# Timeouts (seconds)
HTTP_TIMEOUT=30
CONNECT_TIMEOUT=10
READ_TIMEOUT=60

# Retry Configuration
MAX_RETRIES=3
RETRY_DELAY=5

# Development/Testing
DEVELOPMENT_MODE=false
TEST_MODE=false