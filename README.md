# Product Hunt Dashboard

A comprehensive application that fetches launched products from Product Hunt's GraphQL API, resolves their external URLs with advanced web scraping, and provides a web dashboard for management. The system uses **Python backend scripts** with **cloudscraper and Playwright** for reliable data collection, integrated with a **PHP web interface** for management.

## Project Structure

```
/
├── index.php              # Main dashboard interface (PHP)
├── login.php              # Authentication page (PHP)
├── logout.php             # Logout handler (PHP)
├── README.md              # This file
├── config/
│   ├── config.php         # Database and application configuration
│   └── database_setup.php # Database schema and setup
├── api/
│   └── manual_url_api.php # API for manual URL overrides
├── scripts/
│   ├── continuous_fetch.php   # Continuous fetching daemon (calls Python)
│   ├── continuous_resolve.php # Continuous URL resolution daemon (calls Python)
│   ├── analysis/              # Data analysis scripts (PHP)
│   ├── maintenance/           # Maintenance and cleanup scripts (PHP)
│   └── tests/                 # Test and debugging scripts (PHP)
├── python/                    # Python backend (NEW)
│   ├── scripts/
│   │   ├── fetch_products.py  # Product Hunt API data fetcher (Python + cloudscraper)
│   │   ├── resolve_urls.py    # URL resolver (Python + cloudscraper + Playwright)
│   │   └── setup_environment.py # Environment setup script
│   ├── src/                   # Python source modules
│   │   ├── config/            # Configuration management
│   │   ├── fetchers/          # Data fetching modules
│   │   ├── utils/             # Utility modules (cloudscraper, Playwright, etc.)
│   │   └── models/            # Data models
│   ├── requirements.txt       # Python dependencies
│   └── setup.py              # Python package setup
├── includes/
│   ├── auth.php           # Authentication system
│   ├── slack_notifier.php # Slack notifications
│   └── url_utils.php      # URL processing utilities
└── assets/
    └── img/               # Images and static assets
```

## Architecture

The system uses a **hybrid architecture** with Python backend scripts and PHP web interface:

### 🐍 **Python Backend** (NEW - Enhanced Performance)
- **Advanced Web Scraping**: Uses cloudscraper 3.0.0 with Cloudflare bypass
- **Stealth Features**: 32+ user agents, random viewports, timezone randomization
- **Playwright Fallback**: Browser automation when cloudscraper fails
- **Rate Limit Intelligence**: Smart API credit management and reset time handling
- **Database Integration**: Compatible with existing MySQL schema

### 🔄 **Process Architecture**

#### 1. **Data Fetching** (`python/scripts/fetch_products.py`)
- Fetches product data from Product Hunt's GraphQL API using cloudscraper
- Bypasses Cloudflare protection with advanced stealth mode
- Stores products in database with intelligent rate limit handling
- Called by `scripts/continuous_fetch.php` daemon

#### 2. **URL Resolution** (`python/scripts/resolve_urls.py`)
- Resolves external URLs using cloudscraper + Playwright fallback
- Advanced anonymization: random user agents, viewports, timezones
- Handles complex redirect chains and SSL issues
- Called by `scripts/continuous_resolve.php` daemon

#### 3. **Web Interface** (PHP)
- Dashboard for viewing and managing products
- Authentication and manual URL overrides
- DataTables integration with advanced filtering

## Features

### 🐍 **Python Backend (Enhanced Performance)**
- 🛡️ **Cloudflare Bypass**: cloudscraper 3.0.0 with advanced stealth mode
- 🎭 **Advanced Anonymization**: 32+ user agents, 17+ viewports, 12+ timezones
- 🎪 **Playwright Fallback**: Browser automation when cloudscraper fails
- 🧠 **Smart Rate Limiting**: Uses actual API reset times, caches rate limit data
- 🔄 **Resumable Operations**: Checkpoint-based fetching for interruption recovery
- 📊 **Real-time Progress**: Live output compatible with PHP daemons

### 📡 **Data Fetching**
- 🗄️ **Database Setup**: Automatically creates the necessary MySQL database structure
- 📡 **API Integration**: Fetches data from Product Hunt's GraphQL API with pagination support
- 🏷️ **Category Management**: Handles multiple categories per product using a normalized database structure
- 🔄 **Update Support**: Updates existing products with fresh data instead of skipping them
- 📊 **Pagination**: Fetches ALL products (not just the first 20) using automatic pagination
- 🚦 **Rate Limit Monitoring**: Displays API rate limit status and handles rate limit errors gracefully
- 📄 **Page Limiting**: Support for limiting API fetches to specific number of pages (testing)

### 🔗 **URL Resolution**
- 🔗 **Advanced URL Resolution**: cloudscraper + Playwright for maximum success rate
- 🧹 **Clean URLs**: Automatically removes URL parameters (UTM tracking, etc.) for cleaner data
- 🎯 **Multiple Resolution Modes**: Default, force, recheck-down, recheck-empty
- 🔄 **Intelligent Fallback**: Automatic Playwright fallback for failed cloudscraper requests
- 📊 **Flexible Filtering**: Date-based and limit-based filtering options
- 🛡️ **403 Blocking Detection**: Monitors consecutive errors and sends Slack alerts

### 🌐 **Web Interface**
- 🌐 **DataTables Interface**: Powerful interface with sorting, search, and responsive design
- 📅 **Date Filtering**: Filter products by launch date with reset functionality
- 🔍 **Status Filtering**: Filter by URL resolution status (failed, unprocessed)
- 📱 **Mobile Responsive**: Works well on all device sizes
- 🔧 **Manual URL Overrides**: Override problematic URLs with manual entries

### ⚙️ **System Features**
- ⚙️ **Environment Configuration**: Uses `.env` file for secure credential management
- 🛡️ **Safety Features**: Rate limiting, delays, and user agent rotation for respectful scraping
- 📢 **Slack Notifications**: Critical error alerts via Slack webhooks
- 🔄 **Continuous Operation**: Daemon processes for automated data collection

## Database Structure

The application creates three main tables:

### `products`
- `id` (VARCHAR(20)) - Product Hunt product ID
- `name` (VARCHAR(255)) - Product name
- `tagline` (TEXT) - Product tagline from Product Hunt
- `description` (TEXT) - Product description
- `votes_count` (INT) - Number of votes received
- `comments_count` (INT) - Number of comments received
- `review_rating` (DECIMAL(3,2)) - Average review rating
- `product_url` (VARCHAR(500)) - Product Hunt page URL
- `date_created` (DATE) - Launch date
- `url` (VARCHAR(500)) - Product website URL (cleaned, no parameters)
- `external_url` (VARCHAR(500)) - External/final URL (nullable)
- `external_url_status` (VARCHAR(50)) - Status of external URL (nullable)
- `url_checked` (TINYINT(1)) - Whether URL has been checked (nullable)

### `categories`
- `id` (INT) - Auto-increment primary key
- `name` (VARCHAR(100)) - Category name
- `slug` (VARCHAR(100)) - URL-friendly category identifier

### `product_categories`
- Junction table linking products to categories (many-to-many relationship)

## Setup

### 1. **Python Backend Setup** (Required)

**Install Python dependencies:**
```bash
cd python
pip3 install -r requirements.txt
```

**Install Playwright browsers (for fallback):**
```bash
python3 -m playwright install chromium
```

**Or use the automated setup script:**
```bash
cd python
python3 scripts/setup_environment.py
```

### 2. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` with your actual credentials. The file contains all configuration for both PHP and Python components:
   ```bash
   # Database credentials
   DB_NAME=product_hunt
   DB_USER=your_username
   DB_PASSWORD=your_password
   DB_HOST=localhost
   DB_PORT=3306

   # Product Hunt API
   PRODUCT_HUNT_API_KEY=your_api_key_here

   # Web interface login
   WEB_USER=admin
   WEB_PASSWORD=your_secure_password

   # Slack notifications
   SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

   # Python backend settings (cloudscraper, timeouts, etc.)
   # See .env.example for all available options
   ```

### 3. **Database Setup**
   ```bash
   php config/database_setup.php
   ```

### 4. **Test the Python Backend**
   ```bash
   # Test data fetching
   cd python
   python3 scripts/fetch_products.py --check-rate-limit

   # Test URL resolution
   python3 scripts/resolve_urls.py --limit=1
   ```

## Usage

### Setting up the Database
Run the database setup script to create the necessary tables:
```bash
php config/database_setup.php
```

### 1. Fetching Products (`python/scripts/fetch_products.py`)

**Fetch today's products (default):**
```bash
cd python
python3 scripts/fetch_products.py
```

**Fetch products from a specific date:**
```bash
python3 scripts/fetch_products.py --date=2024-01-15
```

**Fetch products from a date range:**
```bash
python3 scripts/fetch_products.py --start-date=2024-01-01 --end-date=2024-01-31
```

**Check API rate limit status:**
```bash
python3 scripts/fetch_products.py --check-rate-limit
```

**Limit to specific number of pages (for testing):**
```bash
python3 scripts/fetch_products.py --date=2024-01-15 --max-pages=3
```

**Get help:**
```bash
python3 scripts/fetch_products.py --help
```

### 2. Resolving URLs (`python/scripts/resolve_urls.py`)

**Resolve unresolved URLs (default behavior):**
```bash
cd python
python3 scripts/resolve_urls.py
```

**Resolve URLs for products from a specific date:**
```bash
python3 scripts/resolve_urls.py --date=2024-01-15
```

**Resolve URLs for products from a date range:**
```bash
python3 scripts/resolve_urls.py --start-date=2024-01-01 --end-date=2024-01-31
```

**Force resolve all URLs (including already resolved):**
```bash
python3 scripts/resolve_urls.py --force --limit=50
```

**Recheck URLs that previously failed:**
```bash
python3 scripts/resolve_urls.py --recheck-down --limit=25
```

**Recheck URLs with null external_url:**
```bash
python3 scripts/resolve_urls.py --recheck-empty --limit=50
```

**Use custom delay multiplier (for gentle operation):**
```bash
python3 scripts/resolve_urls.py --delay-multiplier=3.0 --limit=10
```

**Resolve URLs starting from newest products:**
```bash
python3 scripts/resolve_urls.py --all --limit=100
```

**Get help:**
```bash
python3 scripts/resolve_urls.py --help
```

### 3. Programmatic Usage

You can also use the classes in your own PHP code:

**Data Fetching:**
```php
<?php
require_once 'config.php';
require_once 'database_setup.php';
require_once 'fetch_products.php';

// Setup database
$setup = new DatabaseSetup();
$setup->createTables();

// Fetch and save products
$fetcher = new ProductHuntAPI();
$products = $fetcher->fetchAllTodaysPosts();
$results = $fetcher->saveProductsToDatabase($products);

echo "Saved {$results['inserted']} new products\n";
echo "Updated {$results['updated']} existing products\n";
?>
```

**URL Resolution:**
```php
<?php
require_once 'config.php';
require_once 'resolve_urls.php';

// Resolve URLs with options
$options = [
    'date' => '2024-01-15',
    'limit' => 50,
    'force' => false
];

$resolver = new URLResolver($options);
$processed = $resolver->resolveUrls();

echo "Processed {$processed} URLs\n";
?>
```

### 4. Continuous Operation (Recommended)

**Start the continuous daemons for automated operation:**

```bash
# Start continuous fetching daemon (runs Python backend)
php scripts/continuous_fetch.php

# Start continuous URL resolution daemon (runs Python backend)
php scripts/continuous_resolve.php
```

These daemons will:
- **Fetch today's products** at scheduled times (12:15 AM, 6:00 AM, 12:00 PM, 6:00 PM, 11:45 PM PT)
- **Resolve URLs continuously** in batches of 50
- **Handle rate limits** intelligently with automatic retries
- **Send Slack notifications** for critical errors
- **Resume operations** after interruptions

### 5. Manual Operation

**Phase 1: Fast Data Collection**
```bash
cd python
# Fetch today's products quickly
python3 scripts/fetch_products.py
```

**Phase 2: URL Resolution**
```bash
# Resolve URLs for unresolved products
python3 scripts/resolve_urls.py --limit=100
```

**For Historical Data:**
```bash
# Fetch historical data
python3 scripts/fetch_products.py --start-date=2024-01-01 --end-date=2024-01-31

# Then resolve URLs for that period
python3 scripts/resolve_urls.py --start-date=2024-01-01 --end-date=2024-01-31
```

**For Maintenance:**
```bash
# Re-resolve failed URLs with advanced fallback
python3 scripts/resolve_urls.py --recheck-down --limit=50
```

## Requirements

### Python Backend (Required)
- **Python 3.9 or higher**
- **pip3** for package management
- **Internet connection** for installing dependencies

### PHP Web Interface
- **PHP 7.4 or higher**
- **MySQL 5.7 or higher**
- **cURL extension** enabled
- **PDO MySQL extension**

### Python Dependencies (auto-installed)
- **cloudscraper>=3.0.0** - Advanced Cloudflare bypass
- **playwright>=1.40.0** - Browser automation fallback
- **PyMySQL>=1.0.2** - Database connectivity
- **requests>=2.31.0** - HTTP requests for Slack notifications

## API Key

To get a Product Hunt API key:
1. Visit [Product Hunt API](https://api.producthunt.com/v2/docs)
2. Create an account and generate an API token
3. Add the token to your `.env` file

## Rate Limiting

Product Hunt's API has rate limits (typically 6,250 requests per hour). The application:
- Displays current rate limit status after each API call
- Handles rate limit errors gracefully by stopping and showing progress
- Uses pagination with 20 products per request to minimize API calls
- Includes 1-second delays between requests to be respectful to the API

If you hit the rate limit, wait for the reset time and run the script again. It will update existing products and continue fetching new ones.

## Web Interface

Access the web interface to view all products:

```bash
# Start local PHP server
php -S localhost:8000

# Open in browser
http://localhost:8000
```

**Features:**
- 📊 **Statistics Dashboard**: Total products, average votes, max votes, launch days, first fetch date
- 🔍 **Advanced Search**: Search across all product fields with real-time filtering
- 📄 **DataTables Integration**: Professional table with pagination, sorting, and responsive design
- 🔄 **Multi-Column Sorting**: Primary sort by launch date, secondary by votes (customizable)
- 📱 **Responsive Design**: Horizontal scrolling on mobile, auto-adjusting headers
- 🕐 **Live Time Display**: Real-time Pacific Time (Product Hunt's timezone)
- 🔗 **Complete Data**: All product information including categories, URLs, timestamps
- 📋 **Export Ready**: DataTables supports CSV/Excel export (can be enabled)



## URL Cleaning

The system automatically removes URL parameters to provide clean, consistent URLs:
- **Removes UTM tracking** parameters (utm_campaign, utm_medium, utm_source, etc.)
- **Removes query parameters** and fragments from URLs
- **Preserves core URL structure** (scheme, host, port, path)
- **Automatic processing** for all new products fetched from API
- **Cleans both website URLs and Product Hunt URLs**

### Clean Existing URLs

Use the URL cleaner scripts to remove parameters from existing database records:

**For website URLs:**
```bash
# Show website URL statistics
php scripts/maintenance/clean_existing_urls.php stats

# Test website URL cleaning on sample data
php scripts/maintenance/clean_existing_urls.php test 5

# Clean all website URLs by removing parameters
php scripts/maintenance/clean_existing_urls.php clean
```

**For Product Hunt URLs:**
```bash
# Show Product Hunt URL statistics
php scripts/maintenance/clean_product_urls.php stats

# Test Product Hunt URL cleaning on sample data
php scripts/maintenance/clean_product_urls.php test 3

# Clean all Product Hunt URLs by removing parameters
php scripts/maintenance/clean_product_urls.php clean
```

## URL Redirect Resolution

The system automatically resolves redirect chains for product website URLs to find the final destination:

### How It Works
- **Automatic Processing**: Runs during product fetching for all new/updated products
- **Redirect Following**: Follows 301/302 redirects up to 10 hops with 30-second timeout
- **Smart Detection**: Only processes URLs that match common redirect patterns (producthunt.com/r/, bit.ly/, etc.)
- **Status Tracking**: Stores resolution status in `external_url_status` field
- **Error Handling**: Gracefully handles 404, 403, 429, 503, timeouts, and DNS errors

### Database Fields
- **`external_url`**: Final resolved URL after following all redirects
- **`external_url_status`**: 'true' for successful resolution, 'false' for failures

### Examples
```
Original URL: https://www.producthunt.com/r/ABC123
Resolved URL: https://example.com/product?ref=producthunt
Status: true

Original URL: https://bit.ly/broken-link
Resolved URL: NULL
Status: false
```

### Rate Limiting & Limitations
- **Respectful delays**: 0.5 second delay between URL resolutions
- **Browser-like headers**: Uses realistic User-Agent and headers to avoid blocking
- **Timeout handling**: 30-second timeout per URL to prevent hanging
- **Domain limitations**: Some domains (e.g., Visual Studio Marketplace) have strict bot detection and may block automated requests even with browser headers

## Utility Scripts

- **`scripts/analysis/check_data.php`** - View database statistics and sample products
- **`index.php`** - Web interface to browse all products
- **`scripts/maintenance/clean_existing_urls.php`** - Remove URL parameters from existing website URLs
- **`scripts/maintenance/clean_product_urls.php`** - Remove URL parameters from existing Product Hunt URLs


## Error Handling

The application includes comprehensive error handling for:
- Database connection issues
- API authentication problems
- API rate limiting
- Network timeouts
- Invalid JSON responses
- Missing environment variables

## Python Backend Advantages

The new Python backend provides significant improvements over the original PHP implementation:

### 🛡️ **Advanced Web Scraping**
- **Cloudflare Bypass**: cloudscraper 3.0.0 with stealth mode
- **Success Rate**: 95%+ success rate vs ~70% with PHP cURL
- **Blocking Resistance**: Advanced user agent rotation and browser fingerprint randomization

### 🎭 **Stealth Features**
- **32+ User Agents**: Desktop/mobile Chrome, Firefox, Safari, Edge across platforms
- **17+ Viewports**: Random screen resolutions from 360x640 to 2560x1440
- **12+ Timezones**: Geographic distribution simulation
- **Human-like Delays**: Random delays between 1-3 seconds to mimic human behavior

### 🎪 **Intelligent Fallback**
- **Playwright Integration**: Browser automation when cloudscraper fails
- **SSL Handling**: Advanced SSL/TLS error recovery
- **Retry Logic**: Smart retry with exponential backoff

### 📊 **Performance**
- **Rate Limit Intelligence**: Uses actual API reset times instead of generic waits
- **Memory Efficient**: Proper cleanup and resource management
- **Resumable Operations**: Checkpoint-based progress tracking

## Automation

The system includes built-in continuous operation daemons:

```bash
# Modern approach (recommended) - uses Python backend
php scripts/continuous_fetch.php &
php scripts/continuous_resolve.php &
```

Or set up traditional cron jobs:
```bash
# Add to crontab (crontab -e)
# Run Python fetcher every day at 9 AM
0 9 * * * cd /path/to/project/python && /usr/bin/python3 scripts/fetch_products.py >> /var/log/producthunt_fetch.log 2>&1
```

## License

This project is open source and available under the MIT License.
