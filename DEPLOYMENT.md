# 🚀 Deployment Guide

## Automated Deployment

The GitHub Actions workflow (`.github/workflows/deploy.yml`) handles most of the deployment automatically when you push to the `main` branch.

### What the Automated Deployment Does:

✅ **File Deployment**
- Syncs all project files to the server
- Excludes development files (.env, .git, __pycache__, etc.)
- Sets proper file permissions

✅ **Python Environment Setup**
- Detects required Python version from `python/.python-version` (currently 3.11.9)
- Installs specific Python version using deadsnakes PPA if needed
- Falls back to default Python 3 if specific version installation fails
- Creates virtual environment in `python/venv/` with correct Python version
- Installs all Python dependencies from `requirements.txt`
- Installs Playwright browsers for fallback functionality
- Verifies Python version and tests the backend

✅ **System Services**
- Reloads/restarts nginx configuration
- Stops any running continuous daemon processes

## Manual Steps After Deployment

### 1. **Environment Configuration** (CRITICAL)

```bash
# SSH to your server
ssh ubuntu@your-server-ip

# Navigate to project directory
cd /var/www/html/

# Create .env file from template
cp .env.example .env

# Edit with your actual credentials
nano .env
```

**Required .env variables:**
```bash
# Database
DB_NAME=product_hunt
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost

# Product Hunt API
PRODUCT_HUNT_API_KEY=your_api_key_here

# Web interface
WEB_USER=admin
WEB_PASSWORD=your_secure_password

# Slack (optional)
SLACK_WEBHOOK_URL=your_slack_webhook
```

### 2. **Database Setup** (First deployment only)

```bash
# Create database and tables
php config/database_setup.php
```

### 3. **Test the System**

```bash
# Test Python backend
cd python
source venv/bin/activate
python scripts/fetch_products.py --check-rate-limit
python scripts/resolve_urls.py --limit=1
deactivate
cd ..

# Test web interface
curl -I http://your-domain.com/
```

### 4. **Start Continuous Daemons** (Optional)

```bash
# Start background processes for automated data collection
nohup php scripts/continuous_fetch.php > logs/continuous_fetch.log 2>&1 &
nohup php scripts/continuous_resolve.php > logs/continuous_resolve.log 2>&1 &

# Check if they're running
ps aux | grep continuous
```

### 5. **Monitor Logs**

```bash
# Check deployment logs
tail -f logs/continuous_fetch.log
tail -f logs/continuous_resolve.log

# Check nginx logs
sudo tail -f /var/log/nginx/error.log
```

## Troubleshooting

### Python Issues
```bash
# Check Python version in virtual environment
cd python
source venv/bin/activate
python --version  # Should show 3.11.9 (or 3.9+ minimum)

# If wrong Python version is installed
deactivate
rm -rf venv
# Manually install Python 3.11
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt-get update
sudo apt-get install python3.11 python3.11-venv python3.11-pip
python3.11 -m venv venv
source venv/bin/activate

# If Python dependencies fail to install
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall

# If cloudscraper version issues
pip install cloudscraper>=1.2.71 --force-reinstall

# If Playwright browsers fail to install
python -m playwright install chromium --force

# If .env file issues
# Make sure .env exists in project root with correct database credentials
cp .env.example .env
nano .env  # Edit with your credentials
```

### Permission Issues
```bash
# Fix file permissions
sudo chown -R ubuntu:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
sudo chmod -R 775 /var/www/html/logs/
```

### Database Issues
```bash
# Check database connection
php -r "
require 'config/config.php';
try {
    \$pdo = new PDO('mysql:host='.DB_HOST.';dbname='.DB_NAME, DB_USER, DB_PASSWORD);
    echo 'Database connection successful\n';
} catch (Exception \$e) {
    echo 'Database error: ' . \$e->getMessage() . '\n';
}
"
```

### Daemon Process Management
```bash
# Stop daemons
sudo pkill -f continuous_fetch.php
sudo pkill -f continuous_resolve.php

# Start daemons
nohup php scripts/continuous_fetch.php > logs/continuous_fetch.log 2>&1 &
nohup php scripts/continuous_resolve.php > logs/continuous_resolve.log 2>&1 &

# Check daemon status
ps aux | grep continuous
```

## Security Notes

- The `.env` file is excluded from deployment for security
- Make sure to set strong passwords in `.env`
- Consider setting up SSL/HTTPS for production
- Regularly update Python dependencies for security patches

## Performance Tips

- Monitor the `logs/` directory for any errors
- The Python backend is much more efficient than the old PHP version
- Consider setting up log rotation for long-running daemons
- Use `htop` to monitor system resources during heavy data fetching
