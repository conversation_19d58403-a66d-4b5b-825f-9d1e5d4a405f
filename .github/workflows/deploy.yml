name: 🚀 Deploy to Production

on:
  push:
    branches:
      - cloudscraper

jobs:
  deploy:
    name: 🎉 Build and Deploy
    runs-on: ubuntu-latest
    env:
      REMOTE_HOST: **************
      REMOTE_USER: ubuntu
      SERVER_PATH: /var/www/html/
      SERVER_USER_AND_GROUP: ubuntu:www-data

      OCI_USER_OCID: ${{ secrets.OCI_USER_OCID }}
      OCI_TENANCY_OCID: ${{ secrets.OCI_TENANCY_OCID }}
      OCI_FINGERPRINT: ${{ secrets.OCI_FINGERPRINT }}
      OCI_REGION: ${{ secrets.OCI_REGION }}
      OCI_SECURITY_RESOURCE_TYPE: 'SL' # Or 'NSG' for Network Security Group
      OCI_SECURITY_RESOURCE_OCID: ${{ secrets.OCI_SECURITY_RESOURCE_OCID }}

    steps:
      - name: 🚚 Get latest codex
        uses: actions/checkout@v3

      # --- OCI IP Whitelisting Steps ---

      - name: 🌍 Get Runner Public IP using action
        id: ip
        uses: haythem/public-ip@v1.3

      - name: 🔍 Cache OCI CLI and dependencies
        uses: actions/cache@v3
        id: cli-cache
        with:
          path: |
            ~/.local/bin
            ~/.local/lib
          key: ${{ runner.os }}-oci-cli-${{ hashFiles('**/requirements.txt', '**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-oci-cli-

      - name: ⚙️ Install OCI CLI and jq
        if: steps.cli-cache.outputs.cache-hit != 'true'
        run: |
          sudo apt-get update && sudo apt-get install -y python3-pip jq
          pip3 install oci-cli --user

      - name: 🔑 Configure OCI CLI
        run: |
          mkdir -p ~/.oci
          echo "[DEFAULT]" > ~/.oci/config
          echo "user=${{ env.OCI_USER_OCID }}" >> ~/.oci/config
          echo "fingerprint=${{ env.OCI_FINGERPRINT }}" >> ~/.oci/config
          echo "tenancy=${{ env.OCI_TENANCY_OCID }}" >> ~/.oci/config
          echo "region=${{ env.OCI_REGION }}" >> ~/.oci/config
          echo "key_file=~/.oci/key.pem" >> ~/.oci/config
          chmod 600 ~/.oci/config # <-- Added fix for permissions warning
          # Store the private key from secret into the key file
          echo "${{ secrets.OCI_API_PRIVATE_KEY }}" > ~/.oci/key.pem
          chmod 600 ~/.oci/key.pem
          echo "OCI CLI Configured."

      - name: 🔥 Whitelist Runner IP in OCI Security Rules
        id: whitelist
        run: |
          if [ -z "${{ steps.ip.outputs.ipv4 }}" ]; then
            echo "::error::Failed to obtain runner public IP address."
            exit 1
          fi
          runner_ip_cidr="${{ steps.ip.outputs.ipv4 }}/32"
          echo "Whitelisting IP CIDR: $runner_ip_cidr for SSH (port 22)"
          RULE_DESCRIPTION="GitHub Actions Runner (${{ github.run_id }}) - $(date +%s)"
          echo "RULE_DESCRIPTION=${RULE_DESCRIPTION}" >> $GITHUB_ENV

          RULE_JSON=$(jq -n --arg ip "$runner_ip_cidr" --arg desc "$RULE_DESCRIPTION" '{
            "direction": "INGRESS",
            "protocol": "6",
            "source": $ip,
            "sourceType": "CIDR_BLOCK",
            "description": $desc,
            "isStateless": false,
            "tcpOptions": {
              "destinationPortRange": {
                "min": 22,
                "max": 22
              }
            }
          }')
          echo "Rule JSON: $RULE_JSON"

          if [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" ]]; then
            echo "Adding rule to Network Security Group (NSG)..."
            # NSG operations might still use SUCCEEDED for the work request
            oci network nsg add-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --security-rules "[$RULE_JSON]" --wait-for-state SUCCEEDED --wait-interval-seconds 5
            echo "Fetching Rule OCID using description..."
            RULE_OCID=$(oci network nsg list-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --all --query "data[?description=='$RULE_DESCRIPTION'].id | [0]" --raw-output)

          elif [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "SL" ]]; then
            echo "Adding rule to Security List (SL)..."
            EXISTING_RULES_JSON=$(oci network security-list get --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --query 'data."ingress-security-rules"' --raw-output)
            UPDATED_RULES_JSON=$(echo "$EXISTING_RULES_JSON" | jq --argjson newrule "$RULE_JSON" '. + [$newrule]')
            # --- FIX IS HERE ---
            oci network security-list update --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --ingress-security-rules "$UPDATED_RULES_JSON" --force --wait-for-state AVAILABLE --wait-interval-seconds 5
            # --- END FIX ---

          else
            echo "::error::Invalid OCI_SECURITY_RESOURCE_TYPE: Must be 'NSG' or 'SL'."
            exit 1
          fi

          if [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" && -n "$RULE_OCID" ]]; then
             echo "Found NSG Rule OCID: $RULE_OCID"
             echo "RULE_OCID=${RULE_OCID}" >> $GITHUB_ENV
          elif [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" && -z "$RULE_OCID" ]]; then
             echo "::warning::Could not reliably determine the OCID of the added NSG rule using description. Removal might fail or remove the wrong rule if descriptions collide."
          fi
          echo "IP Whitelisted successfully."
        env:
          RUNNER_IPV4: ${{ steps.ip.outputs.ipv4 }}

      # --- Deployment Step ---

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_KEY }}
          ARGS: '-avzr --delete'
          REMOTE_HOST: ${{ env.REMOTE_HOST }}
          REMOTE_USER: ${{ env.REMOTE_USER }}
          TARGET: ${{ env.SERVER_PATH }}
          EXCLUDE: ".env,.env.example,.augment/,.github/,.git/,logs/,tools/,python/venv/,python/__pycache__/,python/src/__pycache__/,__pycache__/,*.pyc,.pytest_cache/,test-results/"
          SCRIPT_AFTER: |
            echo "🔧 Setting up file permissions..."
            sudo chown -R ${{ env.SERVER_USER_AND_GROUP }} ${{ env.SERVER_PATH }}

            echo "⚙️ Checking environment configuration..."
            cd ${{ env.SERVER_PATH }}
            if [ ! -f ".env" ]; then
              echo "⚠️ Warning: .env file not found. Please create it manually with your configuration."
              echo "📋 Template available in .env.example"
            else
              echo "✅ .env file found"
            fi

            echo "🐍 Setting up Python environment..."

            # Check for specific Python version requirement
            REQUIRED_PYTHON_VERSION=""
            if [ -f "python/.python-version" ]; then
              REQUIRED_PYTHON_VERSION=$(cat python/.python-version | tr -d '\n\r')
              echo "📋 Required Python version: $REQUIRED_PYTHON_VERSION"
            fi

            # Install Python dependencies
            sudo apt-get update
            sudo apt-get install -y software-properties-common curl build-essential

            # Install specific Python version if specified
            if [ -n "$REQUIRED_PYTHON_VERSION" ]; then
              echo "🔧 Installing Python $REQUIRED_PYTHON_VERSION..."

              # Add deadsnakes PPA for newer Python versions
              sudo add-apt-repository -y ppa:deadsnakes/ppa
              sudo apt-get update

              # Install specific Python version
              PYTHON_PACKAGE="python${REQUIRED_PYTHON_VERSION%.*}"
              sudo apt-get install -y $PYTHON_PACKAGE ${PYTHON_PACKAGE}-venv ${PYTHON_PACKAGE}-pip ${PYTHON_PACKAGE}-dev

              # Create symlink for easier access
              PYTHON_CMD="/usr/bin/$PYTHON_PACKAGE"
              if [ -f "$PYTHON_CMD" ]; then
                echo "✅ Python $REQUIRED_PYTHON_VERSION installed successfully"
              else
                echo "⚠️ Failed to install Python $REQUIRED_PYTHON_VERSION, falling back to default python3"
                sudo apt-get install -y python3 python3-pip python3-venv
                PYTHON_CMD="python3"
              fi
            else
              echo "🔧 Installing default Python 3..."
              sudo apt-get install -y python3 python3-pip python3-venv
              PYTHON_CMD="python3"
            fi

            # Create virtual environment for the project
            if [ ! -d "python/venv" ]; then
              echo "Creating Python virtual environment with $PYTHON_CMD..."
              cd python
              $PYTHON_CMD -m venv venv
              cd ..
            else
              echo "✅ Virtual environment already exists"
            fi

            # Activate virtual environment and install dependencies
            echo "📦 Installing Python dependencies..."
            cd python
            source venv/bin/activate

            # Verify Python version in virtual environment
            echo "🔍 Verifying Python version in virtual environment:"
            python --version

            pip install --upgrade pip
            pip install -r requirements.txt

            # Install Playwright browsers
            echo "Installing Playwright browsers..."
            python -m playwright install chromium

            # Test Python backend
            echo "🧪 Testing Python backend..."
            python scripts/fetch_products.py --check-rate-limit || echo "⚠️ Python backend test failed - check configuration"

            deactivate
            cd ..

            echo "🔄 Managing daemon processes..."
            # Kill any existing continuous processes (they will be restarted manually if needed)
            sudo pkill -f "continuous_fetch.php" || echo "No continuous_fetch.php processes found"
            sudo pkill -f "continuous_resolve.php" || echo "No continuous_resolve.php processes found"
            echo "ℹ️ Note: Restart continuous daemons manually if needed:"
            echo "  php scripts/continuous_fetch.php &"
            echo "  php scripts/continuous_resolve.php &"

            echo "🌐 Reloading nginx configuration..."
            sudo systemctl reload nginx || echo "Failed to reload nginx, trying restart..."
            sudo systemctl restart nginx || echo "Failed to restart nginx"

            echo "✅ Deployment completed successfully!"
            echo "📋 Next steps:"
            echo "  1. Verify .env configuration is correct"
            echo "  2. Start continuous daemons if needed"
            echo "  3. Check logs for any issues"

      # --- OCI IP Removal Step (Always Runs) ---

      - name: 💧 Remove Runner IP from OCI Security Rules
        if: always() && steps.whitelist.outcome == 'success'
        run: |
          if [ -z "${{ steps.ip.outputs.ipv4 }}" ]; then
            echo "::warning::Runner public IP was not available from previous step, cannot perform removal accurately, especially for Security Lists."
            exit 0
          fi
          runner_ip_cidr="${{ steps.ip.outputs.ipv4 }}/32"
          echo "Removing firewall rule for Runner IP CIDR: $runner_ip_cidr"
          RULE_DESCRIPTION="${{ env.RULE_DESCRIPTION }}"
          RULE_OCID="${{ env.RULE_OCID }}"

          if [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "NSG" ]]; then
            if [[ -z "$RULE_OCID" ]]; then
              echo "::warning::NSG Rule OCID was not captured during add. Attempting removal by description."
              RULE_OCID=$(oci network nsg list-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --all --query "data[?description=='$RULE_DESCRIPTION'].id | [0]" --raw-output)
              if [[ -z "$RULE_OCID" ]]; then
                 echo "::error::Could not find NSG rule OCID using description '$RULE_DESCRIPTION'. Manual cleanup required."
                 exit 1
              fi
              echo "Found NSG rule OCID for removal: $RULE_OCID"
            fi
            echo "Removing NSG rule with OCID: $RULE_OCID"
            # NSG operations might still use SUCCEEDED for the work request
            oci network nsg remove-security-rules --nsg-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --security-rule-ids "[\"$RULE_OCID\"]" --force --wait-for-state SUCCEEDED --wait-interval-seconds 5
            echo "NSG Rule removed."

          elif [[ "${{ env.OCI_SECURITY_RESOURCE_TYPE }}" == "SL" ]]; then
            echo "Removing rule from Security List (SL) by filtering..."
            CURRENT_RULES_JSON=$(oci network security-list get --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --query 'data."ingress-security-rules"' --raw-output)
            FILTERED_RULES_JSON=$(echo "$CURRENT_RULES_JSON" | jq --arg desc "$RULE_DESCRIPTION" --arg ip "$runner_ip_cidr" '[.[] | select(.description != $desc and .source != $ip)]')
            if [[ $(echo "$CURRENT_RULES_JSON" | jq 'length') == $(echo "$FILTERED_RULES_JSON" | jq 'length') ]]; then
               echo "::warning::No rule matching description '$RULE_DESCRIPTION' or source IP '$runner_ip_cidr' found in Security List ${{ env.OCI_SECURITY_RESOURCE_OCID }}."
            else
               echo "Updating Security List with filtered rules..."
               # --- FIX IS HERE ---
               oci network security-list update --security-list-id ${{ env.OCI_SECURITY_RESOURCE_OCID }} --ingress-security-rules "$FILTERED_RULES_JSON" --force --wait-for-state AVAILABLE --wait-interval-seconds 5
               # --- END FIX ---
               echo "Security List Rule removed."
            fi

          else
             echo "::error::Invalid OCI_SECURITY_RESOURCE_TYPE encountered during removal."
             exit 1
          fi
          echo "IP rule removal process completed."
        env:
          RUNNER_IPV4: ${{ steps.ip.outputs.ipv4 }}
